// ignore_for_file: prefer_typing_uninitialized_variables, must_be_immutable
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:legacylock_app/data/model/notification.dart';
import 'package:legacylock_app/data/model/question_sends.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart';
import 'package:legacylock_app/helper/consts/colors.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:legacylock_app/pages/tabs/HomeTab/home_tab_stories.dart';
import 'package:legacylock_app/pages/tabs/HomeTab/question_inbox_page.dart';
import 'package:legacylock_app/pages/tabs/HomeTab/question_outbox_page.dart';
import 'package:legacylock_app/router/route_constants.dart';

class HomeTab extends StatefulWidget {
  int? notificationCount;
  int? tabIndex = -1;
  HomeTab({
    Key? key,
    required this.notificationCount,
    this.tabIndex,
  }) : super(key: key);

  @override
  State<HomeTab> createState() => HomeTabState();
}

class HomeTabState extends State<HomeTab> with SingleTickerProviderStateMixin {
  late TabController tabController;
  bool isNotificationOpen = false;

  static HomeTabState? sharedManager;

  static HomeTabState getInstance() {
    return sharedManager!;
  }

  @override
  /// Called when the widget is inserted into the tree.
  ///
  /// This method initialize the [TabController] and sets the initial
  /// tab index based on the [widget.tabIndex] property. If the
  /// [widget.tabIndex] is not -1, it will navigate to the specified
  /// tab and resets the [widget.notificationCount] to 0.
  ///
  /// If the [widget.tabIndex] is -1, it will load the notification
  /// count from the server and call [setState] to update the UI.
  void initState() {
    sharedManager = this;
    tabController = TabController(
      vsync: this,
      length: 4,
    );
    debugPrint("widget.tabIndex ${widget.tabIndex}");
    tabController.addListener(onTap);
    if (widget.tabIndex != -1) {
      if (widget.tabIndex == 3) {
        Future.delayed(const Duration(milliseconds: 10), () {
          if (mounted) {
            Navigator.pushNamed(context, notificationRoute);
          }

          setState(() {
            widget.notificationCount = 0;
          });
        });
      } else {
        tabController.animateTo(widget.tabIndex!);
      }

      widget.tabIndex = -1;
    } else {
      loadNotification();
    }

    super.initState();
  }

  @override

  /// Releases the resources used by the [TabController].
  ///
  /// This method is called when this object is removed from the tree.
  /// It is not called if the object is reused in a different part of the tree.
  ///
  /// The [super.dispose] method is called at the end of this method.
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  /// This function is used to load the notification on the home page.
  ///
  /// The type of notification is checked and the corresponding tab is
  /// animated to. If the type is sentMeQuestion or requestedToConnectWithMe,
  /// the inbox page is loaded. If the type is answeredYourQuestion, the
  /// outbox page is loaded.
  void loadNotification() {
    if (VideoTC().selectedNotification != null) {
      Notifications notifications = VideoTC().selectedNotification!;
      if (notifications.type == NotificationType.sentMeQuestion ||
          notifications.type == NotificationType.requestedToConnectWithMe) {
        tabController.animateTo(1, duration: Duration.zero);
        // VideoTC().selectedNotification = null;
        Future.delayed(const Duration(seconds: 1), () {
          if (QuestionInboxPageState.getInstance() != null) {
            QuestionInboxPageState.getInstance()!.loadAllInboxData();
          } else {
            // debugPrint("QuestionInboxPageState null");
          }
        });
        setState(() {});
      } else if (notifications.type == NotificationType.answeredYourQuestion) {
        tabController.animateTo(3);
        VideoTC().selectedNotification = null;
      }
    }
  }

  /// This function is used to load the notification page.
  ///
  /// If the index of the tab controller is not equal to 3, this function will
  /// animate to 3.
  void loadNotificationPage() {
    if (tabController.index != 3) {
      if (mounted) {
        tabController.animateTo(3);
      }
    }
  }

  resetHomeTab() {
    if (tabController.index != 0) {
      if (mounted) {
        tabController.animateTo(0);
      }
    } else {
      HomeTabStoriesState.getInstance().resetHomeTabStories();
    }
  }

  onTap() {
    if (tabController.index != 0 &&
        VideoTC().questionBloc!.inboxOutboxCountStatus !=
            LoadingStatus.complete) {
      /*int index = tabController.previousIndex;
      setState(() {
        tabController.index = index;
      });*/
    } else if (tabController.index == 3) {
      int index = tabController.previousIndex;
      Navigator.pushNamed(context, notificationRoute);

      setState(() {
        tabController.index = index;
        widget.notificationCount = 0;
      });
    }
  }

  @override
  /// Build the home tab.
  ///
  /// Returns a [Scaffold] widget which contains a [SafeArea] widget as its
  /// body. The [SafeArea] widget has a [Column] widget as its child. The
  /// [Column] widget contains a [SizedBox] widget and a [TabBarView]
  /// widget. The [SizedBox] widget is used to add a space of 5 between the
  /// [TabBar] and the [TabBarView]. The [TabBarView] widget is used to
  /// display the content of the home tab.
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            const SizedBox(
              height: 5,
            ),
            tabs(),
            tabViews(),
          ],
        ),
      ),
    );
  }

  /// Creates and returns a TabBar widget for the HomeTab.
  ///
  /// This TabBar contains four tabs: "Stories", "Inbox", "Outbox", and a
  /// notification tab represented by a bell icon. The "Inbox" tab displays
  /// the count of inbox questions if greater than zero. The notification
  /// tab shows a badge if there are any notifications.
  ///
  /// The TabBar uses the `tabController` for controlling tab changes. It
  /// has customized styling with specific label colors, indicator colors,
  /// and text styles for selected and unselected tabs.

  Widget tabs() {
    // QuestionBloc questionTabBloc = VideoTC().questionBloc!;
    int inboxCount = 0;
    // debugPrint(
    //     "VideoTC().allInboxQuestionList ${VideoTC().allInboxQuestionList.length}");
    for (int i = 0; i < VideoTC().allInboxQuestionList.length; i++) {
      QuestionSends questionSends = VideoTC().allInboxQuestionList[i];

      if (questionSends.video_id == 0) {
        if (questionSends.sent_from_user != null) {
          if (questionSends.trashed_at == "null") {
            inboxCount++;
          }
        }
      }
      /*if (questionSends.video_id == 0) {
        if (questionSends.sent_from_user != null) {
          if (VideoTC().isRequestSentToMe(questionSends.sent_from_user!)) {
            if (VideoTC().isMyFriend(questionSends.sent_from_user!)) {
              inboxCount++;
            }
          } else {
            if (VideoTC().isMyFriend(questionSends.sent_from_user!)) {
              inboxCount++;
            }
          }
        }
      }*/
    }

    inboxCount = inboxCount + VideoTC().allRequestsToYou.length;

    // int outboxCount = questionTabBloc.outboxCount;
    // if (questionTabBloc.questionOutboxListStatus == LoadingStatus.complete) {
    //   outboxCount = VideoTC().allOutboxQuestionList.isEmpty
    //       ? 0
    //       : VideoTC().allOutboxQuestionList.length;
    //   outboxCount = VideoTC().allOutboxQuestionList.length;
    // }
    // double tabWidth = (MediaQuery.of(context).size.width - 50) / 3;
    double tabHeight = 50;
    return Material(
      color: Colors.white,
      child: TabBar(
          controller: tabController,
          labelPadding: const EdgeInsets.symmetric(horizontal: 0),
          isScrollable: false,
          indicatorSize: TabBarIndicatorSize.tab,
          indicatorColor: kLightBlue,
          labelColor: kLightBlue,
          unselectedLabelColor: Color.fromARGB(
            150,
            kLightBlue.red,
            kLightBlue.green,
            kLightBlue.blue,
          ),
          labelStyle: TextStyle(
            fontFamily: getFontFamily(),
            // overflow: TextOverflow.ellipsis,
            fontWeight: FontWeight.bold,
            fontSize: 15,
            color: kBlack,
          ),
          unselectedLabelStyle: TextStyle(
            fontFamily: getFontFamily(),
            overflow: TextOverflow.ellipsis,
            fontWeight: FontWeight.w500,
            fontSize: 15,
            color: kBlack,
          ),
          tabs: [
            Tab(
              height: tabHeight,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(2, 0, 2, 0),
                  child: createText(
                    "Stories",
                    15,
                    fontWeight: FontWeight.bold,
                    textColor: kLightBlue,
                  ),
                ),
              ),
            ),
            Tab(
              height: tabHeight,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(2, 0, 2, 0),
                  child: createText(
                    inboxCount == 0 ? "Inbox" : "Inbox ($inboxCount)",
                    15,
                    fontWeight: FontWeight.bold,
                    textColor: kLightBlue,
                  ),
                ),
              ),
            ),
            Tab(
              height: tabHeight,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(2, 0, 2, 0),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: createText(
                    "Outbox",
                    15,
                    fontWeight: FontWeight.bold,
                    textColor: kLightBlue,
                  ),
                ),
              ),
            ),
            Tab(
                height: tabHeight,
                child: Stack(
                  children: [
                    const Icon(
                      EvaIcons.bellOutline,
                      size: 30,
                    ),
                    Visibility(
                      visible: widget.notificationCount! > 0,
                      child: Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: darkRed,
                            border: Border.fromBorderSide(
                              BorderSide(color: Colors.white, width: 2),
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                )),
          ]),
    );
  }

  /// Creates the tab views for the HomeTab widget.
  ///
  /// The children of the TabBarView are:
  ///
  /// 1. HomeTabStories
  /// 2. QuestionInboxPage
  /// 3. QuestionOutboxPage
  /// 4. An empty SizedBox, used as a placeholder for the fourth tab that is not yet implemented.
  Widget tabViews() {
    return Expanded(
      child: TabBarView(
        controller: tabController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          HomeTabStories(
            questionBloc: VideoTC().questionBloc!,
            parentClass: this,
          ),
          QuestionInboxPage(
            questionBloc: VideoTC().questionBloc!,
            parentClass: this,
          ),
          QuestionOutboxPage(
            questionBloc: VideoTC().questionBloc!,
            parentClass: this,
          ),
          const SizedBox(),
        ],
      ),
    );
  }
}
