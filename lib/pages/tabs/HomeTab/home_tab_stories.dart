import 'package:flutter/material.dart';
import 'package:legacylock_app/data/api/question_bloc.dart';
import 'package:legacylock_app/data/model/video.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/colors.dart';
import 'package:legacylock_app/helper/util/custom_info_page.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:legacylock_app/pages/tabs/Video/video_activity.dart';
import 'package:legacylock_app/pages/tabs/Video/video_api.dart';

class HomeTabStories extends StatefulWidget {
  final QuestionBloc questionBloc;
  // ignore: prefer_typing_uninitialized_variables
  final parentClass;
  const HomeTabStories(
      {Key? key, required this.questionBloc, required this.parentClass})
      : super(key: key);

  @override
  State<HomeTabStories> createState() => HomeTabStoriesState();
}

class HomeTabStoriesState extends State<HomeTabStories> {
  bool noMoreVideos = true;
  List<Video> allVideos = [];
  VideoApi videoApi = VideoApi();
  bool isVideoLoaded = false;

  bool isRecordStory = false;
  bool isConnectOne = false;
  bool isSendQuestion = false;

  bool isTutorialExpanded = true;
  ScrollController scrollController = ScrollController();

  static HomeTabStoriesState? sharedManager;

  /// Returns the [HomeTabStoriesState] instance used by the [HomeTabStories]
  /// widget.
  static HomeTabStoriesState getInstance() {
    return sharedManager!;
  }

  @override
  /// This function is called when this object is inserted into the tree.
  ///
  /// It sets [sharedManager] to this object and then loads the question data,
  /// tutorial data and video data.
  void initState() {
    sharedManager = this;
    // widget.questionBloc.questionListStatus = LoadingStatus.notStarted;
    // widget.questionBloc.tagListStatus = LoadingStatus.notStarted;
    Future.delayed(const Duration(seconds: 0), () async {
      await loadQuestionData();
      loadTotorialData();
      loadVideoData();
    });

    super.initState();
  }

  /// Loads the count of questions in the inbox and outbox.
  ///
  /// This will cause a rebuild of the parent widget, which will update the
  /// question count UI.
  ///
  /// This is called when the widget is first initialized and when the user
  /// sends a question.
  Future<void> loadQuestionData() async {
    await widget.questionBloc.getInboxOutboxCount(this);

    if (widget.parentClass.mounted) {
      widget.parentClass.setState(() {});
    }
  }

  loadTotorialData() async {
    isRecordStory = VideoTC().isRecordStory;
    isConnectOne = VideoTC().isConnectOne;
    isSendQuestion = VideoTC().isSendQuestion;

    if (mounted) {
      setState(() {});
    }
  }

  loadVideoData() async {
    allVideos = [];
    if (videoApi.videoLoadStatus != LoadingStatus.complete) {
      if (videoApi.videoLoadStatus == LoadingStatus.loading) {
        return;
      }
      setState(() {
        videoApi.videoLoadStatus = LoadingStatus.loading;
      });
      allVideos = await videoApi.getHomeFeedVideoList(context);
      videoApi.videoLoadStatus = LoadingStatus.complete;
    }
    if (mounted) {
      setState(() {
        isVideoLoaded = true;
      });
    }
  }

  resetHomeTabStories() async {
    Future.delayed(Duration.zero, () async {
      await loadQuestionData();
      loadTotorialData();
      loadVideoData();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  /// Builds the home tab stories.
  ///
  /// This method is the implementation of the [build] method from the
  /// [StatelessWidget] class. It returns a [Scaffold] widget which contains
  /// a [BottomSheet] and a [RefreshIndicator] widget. The [BottomSheet] widget
  /// displays a tutorial message and a button to record a story. The
  /// [RefreshIndicator] widget displays a list of videos.
  ///
  /// When the user pulls down on the list of videos, the [RefreshIndicator]
  /// widget triggers a refresh of the list of videos. This is done by calling
  /// the [loadVideoData] method.
  ///
  /// The [loadVideoData] method loads the list of videos from the server and
  /// updates the [videoApi] object with the new data. It then calls [setState]
  /// to update the UI.
  ///
  /// The list of videos is displayed in a [ListView] widget. The [ListView]
  /// widget is given a [controller] which is used to scroll the list of videos
  /// to the top when the user pulls down on the list of videos.
  ///
  /// The [loadVideosList] method is called to load the list of videos. This
  /// method returns a [Widget] which displays the list of videos.
  Widget build(BuildContext context) {
    return Scaffold(
      bottomSheet: isRecordStory == false ||
              isConnectOne == false ||
              isSendQuestion == false
          ? BottomSheet(
              backgroundColor: Colors.white,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
              ),
              builder: ((bottomSheetContext) {
                return LayoutBuilder(
                  builder: (context, constraints) {
                    double maxWidth = constraints.maxWidth;
                    return Container(
                      width: MediaQuery.of(context).size.width,
                      height: isTutorialExpanded == true ? 270 : 70,
                      margin: EdgeInsets.only(
                        left: 0,
                        bottom: 70 + MediaQuery.of(context).padding.bottom,
                      ),
                      child: loadTutorial(maxWidth),
                    );
                  },
                );
              }),
              onClosing: () {},
            )
          : const SizedBox(),
      backgroundColor: kPageBackgroundColor,
      body: RefreshIndicator(
        onRefresh: () async {
          videoApi.videoLoadStatus = LoadingStatus.notStarted;
          isVideoLoaded = false;
          await loadVideoData();
          setState(() {});
        },
        child: Padding(
          padding: const EdgeInsets.fromLTRB(15, 7.5, 15, 15),
          child: ListView(
            controller: scrollController,
            children: [
              const SizedBox(
                height: 5,
              ),
              loadVideosList(),
            ],
          ),
        ),
      ),
    );
  }

  /// This widget is used to load the tutorial buttons at the top of the home page.
  /// It is only visible when the user has not completed the tutorial.
  ///
  /// Parameters:
  /// [maxWidth] - The maximum width of the widget
  ///
  /// Returns:
  /// A Column widget with the tutorial buttons.
  Widget loadTutorial(double maxWidth) {
    return Column(
      children: [
        const SizedBox(
          height: 10,
        ),
        Row(
          children: [
            SizedBox(
              width: maxWidth - 65,
              height: 50,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(30, 0, 0, 0),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: createText(
                      "Complete your account setup",
                      17,
                      textColor: kDarkBlue,
                      textAlign: TextAlign.left,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
            IconButton(
              iconSize: 72,
              icon: Icon(
                isTutorialExpanded == false
                    ? Icons.expand_less
                    : Icons.expand_more,
                color: const Color.fromARGB(255, 73, 56, 116),
                size: 30,
              ),
              onPressed: () {
                setState(() {
                  isTutorialExpanded = !isTutorialExpanded;
                });
              },
            ),
          ],
        ),
        isTutorialExpanded == true
            ? Column(
                children: [
                  getTutorialButton(
                      this, "Record your first Story", "📹", 0, isRecordStory,
                      maxWidth: maxWidth),
                  getTutorialButton(
                      this, "Connect with loved ones", "👪", 1, isConnectOne,
                      maxWidth: maxWidth),
                  getTutorialButton(this, "️Send your first Question", "✉️", 2,
                      isSendQuestion,
                      maxWidth: maxWidth),
                ],
              )
            : const SizedBox(),
      ],
    );
  }

  /// Returns a [Widget] which displays a list of videos or a message
  /// when there are no videos.
  ///
  /// If [isVideoLoaded] is false, it returns a shimmering list of videos.
  ///
  /// If [allVideos] is empty, it returns a [CustomInfoPage] with a message
  /// and a png image of a person and a quote from Margaret Atwood.
  ///
  /// Otherwise, it returns a [ListView] widget which displays the list
  /// of videos. The [ListView] widget is given a [ScrollPhysics] which
  /// allows the user to scroll the list of videos. The list of videos is
  /// also given a bottom padding of 80.
  Widget loadVideosList() {
    if (isVideoLoaded == false) return loadStoriesShimmerForProfile(5);
    if (allVideos.isEmpty) {
      return SizedBox(
        child: Column(
          // mainAxisAlignment: MainAxisAlignment.center,
          // crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            isRecordStory == false ||
                    isConnectOne == false ||
                    isSendQuestion == false
                ? const SizedBox()
                : const SizedBox(
                    height: 150,
                  ),
            const CustomInfoPage(
              strImageName: "assets/images/video-empty.png",
              strInfo:
                  "“In the end, we’ll all become stories.”\n- Margaret Atwood",
            ),
          ],
        ),
      );
    }
    return ListView.builder(
      shrinkWrap: true,
      padding: const EdgeInsets.only(bottom: 80),
      // physics: const AlwaysScrollableScrollPhysics(),
      physics: const ScrollPhysics(),
      itemCount: allVideos.length + (noMoreVideos ? 1 : 0),
      itemBuilder: ((context, index) {
        if (noMoreVideos && index == allVideos.length) {
          return const SizedBox(
            height: 60,
          );
        }
        Video video = allVideos[index];
        return VideoActivity().loadVideoCell(this, video, true, false);
      }),
    );
  }

  updateViewCount(Video video) {
    video.viewCount = video.viewCount! + 1;
    if (mounted) {
      setState(() {});
    }
  }

  void deleteComplete(video) {
    deleteCallBack(video);
  }

  deleteCallBack(video) {
    setState(() {
      allVideos.remove(video);
    });
  }

  void updateLikeCount() {
    setState(() {});
  }
}
