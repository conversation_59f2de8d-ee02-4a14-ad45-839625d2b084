import 'package:flutter/material.dart';
import 'package:legacylock_app/data/api/user_profile_bloc.dart';
import 'package:legacylock_app/data/model/invite.dart';
import 'package:legacylock_app/data/model/question_sends.dart';
import 'package:legacylock_app/data/model/relationships.dart';
import 'package:legacylock_app/data/model/user_profile.dart';
import 'package:legacylock_app/data/model/user_relationship.dart';
import 'package:legacylock_app/data/model/video.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart';
import 'package:legacylock_app/helper/consts/colors.dart';
import 'package:legacylock_app/helper/util/app_button.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:legacylock_app/pages/tabs/PeopleTab/relation_page.dart';
import 'package:legacylock_app/pages/tabs/PeopleTab/view_request_page.dart';
import 'package:legacylock_app/pages/tabs/Video/video_activity.dart';
import 'package:legacylock_app/router/route_constants.dart';

class InboxModal extends StatefulWidget {
  const InboxModal({
    Key? key,
  }) : super(key: key);

  @override
  State<InboxModal> createState() => InboxModalState();
}

enum InboxState { waiting, other, done }

class InboxModalState extends State<InboxModal> with TickerProviderStateMixin {
  InboxState inboxState = InboxState.waiting;
  int totalUnAnswerItem = 0;
  int currentInboxIndex = 0;
  ConnectionStatus userConnectionStatus = ConnectionStatus.pending;
  QustionActivityStatus responseStatus = QustionActivityStatus.inbox;
  int questionSendIndex = -1;

  UserProfileBloc userProfileBloc = UserProfileBloc();

  List<UserProfile> allRequestsToYou = [];
  List<UserRelationship> allRequestsToYouRelationship = [];

  List<QuestionSends> allInboxQuestionList = [];

  final PageController progressController = PageController(initialPage: 0);

  String strWaitingTitle = "";
  String strDoneTitle = "You’re all caught up!";
  @override
  void initState() {
    allRequestsToYou.addAll(VideoTC().allRequestsToYou);
    allRequestsToYouRelationship.addAll(VideoTC().allRequestsToYouRelationship);

    setInboxQuestionList();
    totalUnAnswerItem = allRequestsToYou.length + allInboxQuestionList.length;
    strWaitingTitle = "You have $totalUnAnswerItem Inbox items waiting.";

    super.initState();
  }

  setInboxQuestionList() {
    allInboxQuestionList = [];

    for (int i = 0; i < VideoTC().allInboxQuestionList.length; i++) {
      QuestionSends questionSends = VideoTC().allInboxQuestionList[i];
      // debugPrint("questionSends.id ${questionSends.id}");
      // debugPrint("questionSends.video_id ${questionSends.video_id}");
      // debugPrint("questionSends.video ${questionSends.video}");
      if (questionSends.video_id == 0 && questionSends.trashed_at == "null") {
        if (questionSends.sent_from_user != null) {
          allInboxQuestionList.add(questionSends);
        }
      }
    }
  }

  @override
  /// Builds the Inbox modal.
  ///
  /// This widget displays a modal with a progress bar and pages containing
  /// either a list of requests to the user or a question send to the user.
  /// The progress bar is displayed at the top of the modal and shows the
  /// current position of the user in their inbox. The pages are displayed in
  /// a [PageView] widget.
  ///
  /// If the user is at the beginning of their inbox, the progress bar is
  /// hidden and the title of the modal is "You have X Inbox items waiting."
  /// If the user is at the end of their inbox, the progress bar is hidden and
  /// the title of the modal is "You're all caught up!".
  ///
  /// When the user navigates to a new page, the [currentInboxIndex] is updated
  /// and the [loadPageView] method is called to update the UI.
  ///
  Widget build(BuildContext context) {
    // debugPrint("inboxState $inboxState");
    // debugPrint("currentInboxIndex ${MediaQuery.of(context).padding.bottom}");
    double totalHeight = MediaQuery.of(context).size.height;
    if (inboxState == InboxState.waiting) {
      totalHeight = 0;
      TextStyle titleStyle = TextStyle(
        fontFamily: getFontFamily(fontType: FontType.lora),
        fontSize: 18.0,
        fontWeight: FontWeight.bold,
      );
      double titleHeight = calculateTextHeight(
        context,
        strWaitingTitle,
        titleStyle,
        MediaQuery.of(context).size.width - 10,
      );
      totalHeight = 5 + 65 + titleHeight + 50 + 175 + 50 + 65 + 20 + 65 + 55;
    } else if (inboxState == InboxState.done) {
      totalHeight = 0;
      TextStyle titleStyle = TextStyle(
        fontFamily: getFontFamily(fontType: FontType.lora),
        fontSize: 18.0,
        fontWeight: FontWeight.bold,
      );
      double titleHeight = calculateTextHeight(
        context,
        strDoneTitle,
        titleStyle,
        MediaQuery.of(context).size.width - 10,
      );
      totalHeight = 5 + 65 + titleHeight + 20 + 235 + 10 + 65 + 10 + 65;
    }

    return SizedBox(
      height: totalHeight,
      child: Scaffold(
        appBar: AppBar(
          title: Container(
            color: const Color.fromARGB(255, 230, 232, 234),
            width: 50,
            height: 5.0,
          ),
          toolbarHeight:
              inboxState == InboxState.waiting || inboxState == InboxState.done
                  ? 65
                  : MediaQuery.of(context).padding.bottom + 65,
          centerTitle: true,
          automaticallyImplyLeading: false,
          actions: [
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: const Color.fromARGB(255, 245, 245, 245),
                  child: IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: Colors.grey,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
                const SizedBox(
                  width: 10,
                )
              ],
            )
          ],
        ),
        body: inboxState == InboxState.waiting
            ? loadWaitingState()
            : inboxState == InboxState.done
                ? loadDoneState()
                : Column(
                    children: [
                      // const SizedBox(
                      //   height: 10,
                      // ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                        child: LinearProgressIndicator(
                          value: (currentInboxIndex + 1) / totalUnAnswerItem,
                          backgroundColor:
                              const Color.fromARGB(255, 230, 232, 234),
                          color: const Color.fromARGB(255, 64, 54, 74),
                          minHeight: 7,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(10)),
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: createText(
                          "Inbox ${currentInboxIndex + 1} of $totalUnAnswerItem",
                          16.0,
                          textAlign: TextAlign.center,
                          textColor: kDetativateText,
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      //loadPageView(),

                      currentInboxIndex < allRequestsToYou.length
                          ? loadRequestsToYou(currentInboxIndex)
                          : loadQuestionSends(
                              currentInboxIndex - allRequestsToYou.length),
                    ],
                  ),
      ),
    );
  }

  /// This widget is used to display the waiting state of the inbox modal.
  ///
  /// This is the first page of the inbox modal and it displays the title of
  /// the inbox modal, an image, and two buttons. The first button is used to
  /// check the inbox and the second button is used to dismiss the inbox modal.
  ///
  /// The waiting state is used when the inbox modal is first opened and the
  /// user has not yet checked their inbox.
  Widget loadWaitingState() {
    return Column(
      children: [
        SizedBox(
          width: MediaQuery.of(context).size.width - 10,
          child: createText(
            strWaitingTitle,
            18.0,
            fontWeight: FontWeight.bold,
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(
          height: 50,
        ),
        Image.asset(
          "assets/images/inbox-modal.png",
          // fit: BoxFit.cover,
          width: 270,
          // height: 270,
        ),
        const SizedBox(
          height: 50,
        ),
        ButtonView(
          buttonText: "Check my Inbox",
          borderRadius: 30,
          fontSize: 20,
          buttonTextColor: kWhiteColor,
          bgColor: kLightBlue,
          onClicked: () {
            setState(() {
              currentInboxIndex = 0;
              inboxState = InboxState.other;
            });
          },
        ),
        const SizedBox(
          height: 20,
        ),
        ButtonView(
          buttonText: "Not now",
          borderRadius: 30,
          fontSize: 20,
          buttonTextColor: kDetativateText,
          bgColor: kWhiteColor,
          onClicked: () {
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }

  /// This widget is used to display the questions and requests to you in a
  /// [PageView].
  ///
  /// The [PageView] is used to display the questions and requests to you in a
  /// scrollable list. The [PageView] is given a height of 700 and a
  /// [NeverScrollableScrollPhysics] so that the user can not scroll the list.
  ///
  /// The [PageView] is also given a [PageController] which is used to control
  /// the page that is currently displayed. The [PageController] is also used to
  /// animate the page changes when the user taps on the next or previous
  /// question or request.
  ///
  /// The [PageView] displays the questions and requests to you in the same way
  /// as the inbox modal, but it is displayed in a scrollable list rather than
  /// on separate pages.
  ///
  /// The [PageView] is used to display the questions and requests to you when
  /// the user has checked their inbox and wants to view the questions and
  /// requests to them.
  Widget loadPageView() {
    return SizedBox(
      height: 700,
      child: PageView.builder(
        controller: progressController,
        physics: const NeverScrollableScrollPhysics(),
        onPageChanged: (int page) {
          setState(() {
            inboxState = InboxState.other;
          });
        },
        itemCount: totalUnAnswerItem,
        itemBuilder: (BuildContext context, int index) {
          return index < allRequestsToYou.length
              ? loadRequestsToYou(currentInboxIndex)
              : const SizedBox();
        },
      ),
    );
  }

  loadPopupItem(String strText, popupCallBack) {
    return TextButton(
      onPressed: () {
        popupCallBack();
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(0.0),
        ),
      ),
      child: createText(
        strText,
        18,
        textColor: kLightBlue,
        fontWeight: FontWeight.bold,
        fontType: FontType.lato,
      ),
    );
  }

  loadConectionMenuItem(
      UserProfile userProfile, UserRelationship userRelationship) {
    return Row(
      children: [
        const SizedBox(
          width: 15,
        ),
        Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            // color: Colors.red,
            border: Border.all(
              color: const Color.fromARGB(255, 214, 214, 214), // Border color
              width: 2.0, // Border thickness
            ),
            borderRadius: BorderRadius.circular(15),
          ),
          child: PopupMenuButton(
            padding: const EdgeInsets.all(0.0),
            color: Colors.white,
            icon: const Icon(
              Icons.more_vert,
              size: 25,
              // color: tertiaryColor,
            ),
            offset: const Offset(0, 25),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            itemBuilder: (context) => [
              PopupMenuItem(
                child: loadPopupItem(
                  "View Request",
                  () => {
                    Navigator.of(context).pop(),
                    openViewRequestPage(
                      this,
                      userProfile,
                      userRelationship,
                      userRelationship.name_as_from!,
                      userRelationship.name_as_outsider!,
                      Invite(),
                      RequestType.approveRequest,
                    ),
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// This widget is used to display the questions and requests to you in a
  /// [PageView].
  ///
  /// The [PageView] is used to display the questions and requests to you in a
  /// scrollable list. The [PageView] is given a height of 700 and a
  /// [NeverScrollableScrollPhysics] so that the user can not scroll the list.
  ///
  /// The [PageView] is also given a [PageController] which is used to control
  /// the page that is currently displayed. The [PageController] is also used to
  /// animate the page changes when the user taps on the next or previous
  /// question or request.
  ///
  /// The [PageView] displays the questions and requests to you in the same way
  /// as the inbox modal, but it is displayed in a scrollable list rather than
  /// on separate pages.
  ///
  /// The [PageView] is used to display the questions and requests to you when
  /// the user has checked their inbox and wants to view the questions and
  /// requests to them.
  Widget loadRequestsToYou(int index) {
    UserProfile userProfile = allRequestsToYou[index];
    UserRelationship userRelationship = allRequestsToYouRelationship[index];
    int uploadTimeInSec = ((DateTime.now().millisecondsSinceEpoch -
                DateTime.parse(userRelationship.created_at!)
                    .millisecondsSinceEpoch) /
            1000)
        .round();
    String strUploadSince = VideoActivity().getUploadTimeSince(uploadTimeInSec);

    return Column(
      children: [
        Container(
          height: 450,
          color: Colors.transparent,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              createText(
                userConnectionStatus == ConnectionStatus.approved
                    ? "✓ You’re Connected to ${userProfile.first_name}!"
                    : userConnectionStatus == ConnectionStatus.discarded
                        ? "You discarded ${userProfile.first_name}'s \nfriend request!"
                        : "",
                20.0,
                fontWeight: FontWeight.bold,
                textColor: const Color.fromARGB(255, 32, 53, 120),
              ),
              const SizedBox(
                height: 30,
              ),
              Container(
                width: MediaQuery.of(context).size.width - 20,
                height: 320,
                decoration: BoxDecoration(
                  color: userConnectionStatus == ConnectionStatus.approved
                      ? const Color.fromARGB(255, 239, 239, 239)
                      : Colors.white,
                  borderRadius: const BorderRadius.all(Radius.circular(20)),
                ),
                child: Align(
                  alignment: Alignment.topCenter,
                  child: Column(
                    children: [
                      VideoActivity().loadActivity(
                        context,
                        userProfile,
                        true,
                        VideoTC().currentUserProfile,
                        "${userProfile.first_name} requested to connect with you.",
                        strUploadSince,
                        xOffset:
                            userConnectionStatus == ConnectionStatus.pending
                                ? 45
                                : 0,
                        aditionWidget:
                            userConnectionStatus == ConnectionStatus.pending
                                ? loadConectionMenuItem(
                                    userProfile, userRelationship)
                                : const SizedBox(),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          getConnectUserInfoPolish(
                            context: context,
                            isCurrentUser: false,
                            userProfile: userProfile,
                            relationShipState: RelationShipState.sendRequest,
                            strRelationToOther: userRelationship.name_as_from!,
                            strRelationToMe: userRelationship.name_as_outsider!,
                            bgColor: userConnectionStatus ==
                                    ConnectionStatus.approved
                                ? const Color.fromARGB(255, 239, 239, 239)
                                : Colors.white,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          getConnectUserInfoPolish(
                            context: context,
                            isCurrentUser: true,
                            userProfile: userProfile,
                            relationShipState: RelationShipState.sendRequest,
                            strRelationToOther: userRelationship.name_as_from!,
                            strRelationToMe: userRelationship.name_as_outsider!,
                            bgColor: userConnectionStatus ==
                                    ConnectionStatus.approved
                                ? const Color.fromARGB(255, 239, 239, 239)
                                : Colors.white,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        if (userConnectionStatus == ConnectionStatus.pending) ...[
          ButtonView(
            buttonText: "Connect with ${userProfile.first_name}",
            borderRadius: 30,
            fontSize: 20,
            buttonTextColor: kWhiteColor,
            bgColor: kLightBlue,
            onClicked: () {
              setConnectionStatus(
                this,
                userProfile,
                userRelationship,
                userRelationship.name_as_outsider!,
                userRelationship.name_as_from!,
                false,
                ConnectionStatus.approved,
                Invite(),
                willAlertShow: false,
              );
            },
          ),
          const SizedBox(
            height: 20,
          ),
          ButtonView(
            buttonText: "Skip",
            borderRadius: 30,
            fontSize: 20,
            buttonTextColor: kDetativateText,
            bgColor: kWhiteColor,
            onClicked: () {
              // progressController.animateToPage(currentInboxIndex,
              //     duration: const Duration(microseconds: 500),
              //     curve: Curves.ease);
              loadNextState();
            },
          ),
        ] else ...[
          ButtonView(
            buttonText: "Next",
            borderRadius: 30,
            fontSize: 20,
            buttonTextColor: kWhiteColor,
            bgColor: kLightBlue,
            onClicked: () {
              responseStatus = QustionActivityStatus.inbox;
              userConnectionStatus = ConnectionStatus.pending;
              loadNextState();
            },
          ),
        ]
      ],
    );
  }

  connectionStatusDone(
      ConnectionStatus connectionStatus,
      UserProfile userProfile,
      UserRelationship userRelationship,
      Relationships relationships,
      {bool isCurrentUserDone = false}) {
    VideoTC().connectionStatusDone(
        connectionStatus, userProfile, userRelationship, relationships,
        isCurrentUserDone: isCurrentUserDone);
    debugPrint("inbox modal connectionStatusDone $connectionStatus");
    switch (connectionStatus) {
      case ConnectionStatus.pending:
        break;
      case ConnectionStatus.approved:
        setState(() {
          userConnectionStatus = ConnectionStatus.approved;
        });
        break;
      case ConnectionStatus.invited:
        debugPrint("ConnectionStatus.invited");
        break;
      case ConnectionStatus.discarded:
        {
          setState(() {
            userConnectionStatus = ConnectionStatus.discarded;
          });
        }
        break;
      case ConnectionStatus.resent:
        debugPrint("ConnectionStatus.resent");
        break;
      case ConnectionStatus.recover:
        debugPrint("ConnectionStatus.recover");
        break;
    }
  }

  loadFriendMenuItem(QuestionSends questionSends) {
    return Row(
      children: [
        const SizedBox(
          width: 15,
        ),
        Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            // color: Colors.red,
            border: Border.all(
              color: const Color.fromARGB(255, 214, 214, 214), // Border color
              width: 2.0, // Border thickness
            ),
            borderRadius: BorderRadius.circular(15),
          ),
          child: PopupMenuButton(
            padding: const EdgeInsets.all(0.0),
            color: Colors.white,
            icon: const Icon(
              Icons.more_vert,
              size: 25,
              // color: tertiaryColor,
            ),
            offset: const Offset(0, 25),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            itemBuilder: (context) => [
              PopupMenuItem(
                child: loadPopupItem(
                  "Discard question",
                  () => {
                    Navigator.of(context).pop(),
                    //discardQustionSendDone(questionSends),
                    VideoTC()
                        .questionBloc!
                        .discardQustionSend(this, questionSends),
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  discardQustionSendDone(QuestionSends questionSends) {
    questionSends.trashed_at = DateTime.now().toString();
    responseStatus = QustionActivityStatus.discarded;
    setState(() {});
  }


  /// Updates the user's profile with the newly uploaded video, and sets the 
  /// response status to answered.
  void videoUploadDone(Video video) {
    video.owner = Owner.fromUserProfile(VideoTC().currentUserProfile!);
    VideoTC().currentUserProfile!.stories =
        VideoTC().currentUserProfile!.stories! + 1;

    setState(() {
      responseStatus = QustionActivityStatus.answered;
    });
  }
/// Loads and displays a `QuestionSends` widget based on the specified index in 
/// the inbox question list. It determines the relationship status with the 
/// sender and configures the UI accordingly, including displaying the question 
/// text, sender details, and interaction buttons like "Record my response," 
/// "Skip," or "Next." The UI changes dynamically based on the friendship status 
/// and the current response status. If the sender is a friend, the user can 
/// directly interact with the question; otherwise, appropriate prompts are shown 
/// for managing friend requests.


/// Loads and displays a widget for a question in the inbox based on the given
/// index. It checks the friendship status with the sender to determine whether
/// the user can directly respond to the question or needs to handle a friend
/// request. The widget includes details such as the question text, sender
/// information, and action buttons like "Record my response" or "Skip". The
/// UI dynamically changes based on the response and connection status.

  Widget loadQuestionSends(int index) {
    QuestionSends questionSends = allInboxQuestionList[index];
    bool isMyFriend = false;
    if (VideoTC().isMyFriend(questionSends.sent_from_user!)) {
      isMyFriend = true;
    }

    int askQuestionTimeInSec = ((DateTime.now().millisecondsSinceEpoch -
                DateTime.parse(questionSends.created_at!)
                    .millisecondsSinceEpoch) /
            1000)
        .round();
    String strAskQuestionTime =
        VideoActivity().getUploadTimeSince(askQuestionTimeInSec);

    UserProfile userProfile = questionSends.sent_from_user!;
    UserRelationship userRelationship =
        VideoTC().getUserRelationWithSelf(userProfile.id!);

    String strUploadSince = "";
    if (userRelationship.id != null) {
      if (isMyFriend == false) {
        userConnectionStatus = ConnectionStatus.pending;
      }
      int uploadTimeInSec = ((DateTime.now().millisecondsSinceEpoch -
                  DateTime.parse(userRelationship.created_at!)
                      .millisecondsSinceEpoch) /
              1000)
          .round();
      strUploadSince = VideoActivity().getUploadTimeSince(uploadTimeInSec);
    } else {
      userConnectionStatus = ConnectionStatus.discarded;
    }

    return Column(
      children: [
        Container(
          height: 450,
          color: Colors.transparent,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              createText(
                userConnectionStatus == ConnectionStatus.discarded
                    ? "You discarded ${userProfile.first_name}'s \nfriend request!"
                    : responseStatus == QustionActivityStatus.answered
                        ? "✓ Your response was uploaded to\nyour profile!"
                        : responseStatus == QustionActivityStatus.discarded
                            ? "You discarded this Question"
                            : "",
                20.0,
                fontWeight: FontWeight.bold,
                textColor: const Color.fromARGB(255, 32, 53, 120),
              ),
              const SizedBox(
                height: 30,
              ),
              Container(
                width: MediaQuery.of(context).size.width - 20,
                height: 320,
                decoration: BoxDecoration(
                  color: responseStatus == QustionActivityStatus.answered
                      ? const Color.fromARGB(255, 239, 239, 239)
                      : Colors.white,
                  borderRadius: const BorderRadius.all(Radius.circular(20)),
                ),
                child: Align(
                  alignment: Alignment.topCenter,
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      if (isMyFriend == true) ...[
                        VideoActivity().loadActivity(
                          context,
                          questionSends.sent_from_user,
                          true,
                          VideoTC().currentUserProfile,
                          "${questionSends.sent_from_user!.getFullName()} asked you...",
                          strAskQuestionTime,
                          xOffset: responseStatus == QustionActivityStatus.inbox
                              ? 45
                              : 0,
                          aditionWidget:
                              responseStatus == QustionActivityStatus.inbox
                                  ? loadFriendMenuItem(questionSends)
                                  : const SizedBox(),
                        ),
                      ] else ...[
                        if (userConnectionStatus ==
                            ConnectionStatus.pending) ...[
                          VideoActivity().loadActivity(
                            context,
                            userProfile,
                            true,
                            VideoTC().currentUserProfile,
                            "${userProfile.first_name} requested to connect with you.",
                            strUploadSince,
                            xOffset:
                                responseStatus == QustionActivityStatus.inbox
                                    ? 45
                                    : 0,
                            aditionWidget:
                                responseStatus == QustionActivityStatus.inbox
                                    ? loadNoFriendMenuItem(userProfile,
                                        userRelationship, questionSends)
                                    : const SizedBox(),
                          ),
                          VideoActivity().loadActivity(
                            context,
                            questionSends.sent_from_user,
                            true,
                            VideoTC().currentUserProfile,
                            "${questionSends.sent_from_user!.getFullName()} asked you...",
                            strAskQuestionTime,
                          ),
                        ] else ...[
                          VideoActivity().loadActivity(
                            context,
                            questionSends.sent_from_user,
                            true,
                            VideoTC().currentUserProfile,
                            "${questionSends.sent_from_user!.getFullName()} asked you...",
                            strAskQuestionTime,
                          ),
                        ]
                      ],
                      const SizedBox(
                        height: 10,
                      ),
                      createText(
                        questionSends.question!.question_text!,
                        20.0,
                        fontWeight: FontWeight.bold,
                        textColor: kLightBlue,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        if (responseStatus == QustionActivityStatus.inbox &&
            userConnectionStatus != ConnectionStatus.discarded) ...[
          ButtonView(
            buttonText: "Record my response",
            borderRadius: 30,
            fontSize: 20,
            buttonTextColor: isMyFriend == true ? kWhiteColor : kLightBlue,
            bgColor: isMyFriend == true
                ? kLightBlue
                : const Color.fromARGB(255, 218, 218, 218),
            buttonTextWidget: isMyFriend == true
                ? const SizedBox()
                : const Row(
                    children: [
                      SizedBox(
                        width: 15,
                      ),
                      Icon(
                        size: 35,
                        Icons.info_outline_rounded,
                        color: Color.fromARGB(255, 28, 16, 43),
                      ),
                    ],
                  ),
            onClicked: () {
              if (isMyFriend == true) {
                //videoUploadDone(Video());
                VideoTC().questionBloc!.questionInboxListStatus =
                    LoadingStatus.notStarted;
                questionSendIndex =
                    VideoTC().allInboxQuestionList.indexOf(questionSends);

                VideoTC().questionBloc!.videoSentUserId =
                    questionSends.sent_from_user_id;
                VideoTC().questionBloc!.questionSentId = questionSends.id;
                VideoTC().questionBloc!.questionId = questionSends.question_id;
                VideoTC().questionBloc!.questionSendsToAnswser = questionSends;
                Navigator.pushNamed(
                  context,
                  recordRoute,
                  arguments: {
                    "questions": questionSends.question!,
                    "questionBloc": VideoTC().questionBloc,
                    "parentClass": this,
                  },
                );
              } else {
                openViewRequestPage(
                  this,
                  userProfile,
                  userRelationship,
                  userRelationship.name_as_from!,
                  userRelationship.name_as_outsider!,
                  Invite(),
                  RequestType.approveRequest,
                );
              }
            },
          ),
          const SizedBox(
            height: 20,
          ),
          ButtonView(
            buttonText: "Skip",
            borderRadius: 30,
            fontSize: 20,
            buttonTextColor: kDetativateText,
            bgColor: kWhiteColor,
            onClicked: () {
              setState(
                () {
                  loadNextState();
                },
              );
            },
          ),
        ] else ...[
          ButtonView(
            buttonText: "Next",
            borderRadius: 30,
            fontSize: 20,
            buttonTextColor: kWhiteColor,
            bgColor: kLightBlue,
            onClicked: () {
              responseStatus = QustionActivityStatus.inbox;
              userConnectionStatus = ConnectionStatus.pending;
              loadNextState();
            },
          ),
        ]
      ],
    );
  }

  loadNoFriendMenuItem(UserProfile userProfile,
      UserRelationship userRelationship, QuestionSends questionSends) {
    return Row(
      children: [
        const SizedBox(
          width: 15,
        ),
        Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            // color: Colors.red,
            border: Border.all(
              color: const Color.fromARGB(255, 214, 214, 214), // Border color
              width: 2.0, // Border thickness
            ),
            borderRadius: BorderRadius.circular(15),
          ),
          child: PopupMenuButton(
            padding: const EdgeInsets.all(0.0),
            color: Colors.white,
            icon: const Icon(
              Icons.more_vert,
              size: 25,
              // color: tertiaryColor,
            ),
            offset: const Offset(0, 50),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            itemBuilder: (context) => [
              PopupMenuItem(
                child: loadPopupItem(
                  "View connection request",
                  () => {
                    Navigator.of(context).pop(),
                    openViewRequestPage(
                      this,
                      userProfile,
                      userRelationship,
                      userRelationship.name_as_from!,
                      userRelationship.name_as_outsider!,
                      Invite(),
                      RequestType.approveRequest,
                    ),
                  },
                ),
              ),
              PopupMenuItem(
                child: loadPopupItem(
                  "Discard question",
                  () => {
                    Navigator.of(context).pop(),
                    discardQustionSendDone(questionSends),
                    /*VideoTC()
                        .questionBloc!
                        .discardQustionSend(this, questionSends),*/
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  openRelationPage(UserProfile userProfile) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      constraints: const BoxConstraints(
        maxWidth: double.infinity,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(25.0),
      ),
      backgroundColor: Colors.white,
      builder: (bc) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.85,
          child: RelationPage(
            parentClass: this,
            userProfile: userProfile,
            userProfileBloc: userProfileBloc,
          ),
        );
      },
    );
  }

  /*Widget loadQuestionSendsNoFriend(int index) {
    QuestionSends questionSends = allInboxQuestionNoFriendList[index];
    int askQuestionTimeInSec = ((DateTime.now().millisecondsSinceEpoch -
                DateTime.parse(questionSends.created_at!)
                    .millisecondsSinceEpoch) /
            1000)
        .round();
    String strAskQuestionTime =
        VideoActivity().getUploadTimeSince(askQuestionTimeInSec);

    UserProfile userProfile = questionSends.sent_from_user!;
    UserRelationship userRelationship =
        VideoTC().getUserRelationWithSelf(userProfile.id!);

    int uploadTimeInSec = ((DateTime.now().millisecondsSinceEpoch -
                DateTime.parse(questionSends.created_at!)
                    .millisecondsSinceEpoch) /
            1000)
        .round();
    String strUploadSince = VideoActivity().getUploadTimeSince(uploadTimeInSec);

    return Column(
      children: [
        Container(
          height: 450,
          color: Colors.transparent,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              createText(
                responseStatus == QustionActivityStatus.answered
                    ? "✓ Your response was uploaded to\nyour profile!"
                    : responseStatus == QustionActivityStatus.discarded
                        ? "You discarded this Question"
                        : "",
                20.0,
                fontWeight: FontWeight.bold,
                textColor: const Color.fromARGB(255, 32, 53, 120),
              ),
              const SizedBox(
                height: 30,
              ),
              Container(
                width: MediaQuery.of(context).size.width - 20,
                height: 320,
                decoration: BoxDecoration(
                  color: responseStatus == QustionActivityStatus.answered
                      ? const Color.fromARGB(255, 239, 239, 239)
                      : Colors.white,
                  borderRadius: const BorderRadius.all(Radius.circular(20)),
                ),
                child: Align(
                  alignment: Alignment.topCenter,
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      VideoActivity().loadActivity(
                        context,
                        userProfile,
                        true,
                        VideoTC().currentUserProfile,
                        "${userProfile.first_name} requested to connect with you.",
                        strUploadSince,
                        xOffset: responseStatus == QustionActivityStatus.inbox
                            ? 45
                            : 0,
                        aditionWidget: responseStatus ==
                                QustionActivityStatus.inbox
                            ? loadNoFriendMenuItem(
                                userProfile, userRelationship, questionSends)
                            : const SizedBox(),
                      ),
                      VideoActivity().loadActivity(
                        context,
                        questionSends.sent_from_user,
                        true,
                        VideoTC().currentUserProfile,
                        "${questionSends.sent_from_user!.getFullName()} asked you...",
                        strAskQuestionTime,
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      createText(
                        questionSends.question!.question_text!,
                        20.0,
                        fontWeight: FontWeight.bold,
                        textColor: kLightBlue,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        if (responseStatus == QustionActivityStatus.inbox) ...[
          ButtonView(
            buttonText: "Record my response",
            borderRadius: 30,
            fontSize: 20,
            buttonTextColor: kLightBlue,
            buttonTextWidget: const Row(
              children: [
                SizedBox(
                  width: 15,
                ),
                Icon(
                  size: 35,
                  Icons.info_outline_rounded,
                  color: Color.fromARGB(255, 28, 16, 43),
                ),
              ],
            ),
            bgColor: const Color.fromARGB(255, 218, 218, 218),
            onClicked: () {
              openViewRequestPage(
                this,
                userProfile,
                userRelationship,
                userRelationship.name_as_from!,
                userRelationship.name_as_outsider!,
                Invite(),
                RequestType.approveRequest,
              );
              /* // videoUploadDone(Video());
              VideoTC().questionBloc!.questionInboxListStatus =
                  LoadingStatus.notStarted;
              questionSendIndex =
                  VideoTC().allInboxQuestionList.indexOf(questionSends);

              VideoTC().questionBloc!.videoSentUserId =
                  questionSends.sent_from_user_id;
              VideoTC().questionBloc!.questionSentId = questionSends.id;
              VideoTC().questionBloc!.questionId = questionSends.question_id;
              VideoTC().questionBloc!.questionSendsToAnswser = questionSends;
              Navigator.pushNamed(
                context,
                recordRoute,
                arguments: {
                  "questions": questionSends.question!,
                  "questionBloc": VideoTC().questionBloc,
                  "parentClass": this,
                },
              );*/
            },
          ),
          const SizedBox(
            height: 20,
          ),
          ButtonView(
            buttonText: "Skip",
            borderRadius: 30,
            fontSize: 20,
            buttonTextColor: kDetativateText,
            bgColor: kWhiteColor,
            onClicked: () {
              setState(
                () {
                  loadNextState();
                },
              );
            },
          ),
        ] else ...[
          ButtonView(
            buttonText: "Next",
            borderRadius: 30,
            fontSize: 20,
            buttonTextColor: kWhiteColor,
            bgColor: kLightBlue,
            onClicked: () {
              responseStatus = QustionActivityStatus.inbox;
              userConnectionStatus = ConnectionStatus.pending;
              loadNextState();
            },
          ),
        ]
      ],
    );
  }*/

  /// Returns a widget that displays a done state for the question inbox modal.
  ///
  /// This widget is used to display the done state for the question inbox modal.
  /// It displays a title, an image, and a button to close the modal.
  ///
  /// The title is centered and has a font size of 18.0 and a bold font weight.
  ///
  /// The image is an asset image with a height of 270.
  ///
  /// The button has a rounded rectangle shape with a corner radius of 30,
  /// a font size of 20, a white text color, and a light blue background color.
  /// When the button is clicked, it closes the modal.
  Widget loadDoneState() {
    return Column(
      children: [
        SizedBox(
          width: MediaQuery.of(context).size.width - 10,
          child: createText(
            strDoneTitle,
            18.0,
            fontWeight: FontWeight.bold,
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(
          height: 20,
        ),
        Image.asset(
          "assets/images/question_inbox_info_2.png",
          fit: BoxFit.cover,
          // width: 270,
          height: 270,
        ),
        const SizedBox(
          height: 10,
        ),
        ButtonView(
          buttonText: "Done",
          borderRadius: 30,
          fontSize: 20,
          buttonTextColor: kWhiteColor,
          bgColor: kLightBlue,
          onClicked: () {
            Navigator.of(context).pop();
          },
        ),
        const SizedBox(
          height: 10,
        ),
      ],
    );
  }

  loadNextState() {
    setState(() {
      if (currentInboxIndex < (totalUnAnswerItem - 1)) {
        currentInboxIndex++;
        inboxState = InboxState.other;
      } else {
        inboxState = InboxState.done;
      }
    });
  }
}
