// ignore_for_file: must_be_immutable, prefer_typing_uninitialized_variables
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:legacylock_app/data/api/user_profile_bloc.dart';
import 'package:legacylock_app/data/model/invite.dart';
import 'package:legacylock_app/data/model/notification.dart';
import 'package:legacylock_app/data/model/relationships.dart';
import 'package:legacylock_app/data/model/tags.dart';
import 'package:legacylock_app/data/model/user_profile.dart';
import 'package:legacylock_app/data/model/user_relationship.dart';
import 'package:legacylock_app/data/model/video.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart';
import 'package:legacylock_app/helper/consts/colors.dart';
import 'package:legacylock_app/helper/util/custom_info_page.dart';
import 'package:legacylock_app/helper/util/warning.dart';
import 'package:legacylock_app/pages/main_page.dart';
import 'package:legacylock_app/pages/tabs/PeopleTab/relation_page.dart';
import 'package:legacylock_app/pages/tabs/PeopleTab/view_request_page.dart';
import 'package:legacylock_app/pages/tabs/Video/video_activity.dart';
import 'package:legacylock_app/pages/tabs/Video/video_api.dart';
import 'package:legacylock_app/router/route_constants.dart';
import 'package:shimmer/shimmer.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:legacylock_app/helper/util/view_util.dart';

enum ProfileUserType {
  self,
  unApproved,
  friend,
  noConnection,
}

class ProfileTab extends StatefulWidget {
  final int userId;
  final bool isProfileTab;
  final parentClass;

  const ProfileTab({
    Key? key,
    this.userId = -1,
    this.isProfileTab = true,
    this.parentClass,
  }) : super(key: key);

  @override
  State<ProfileTab> createState() => ProfileTabState();
}

class ProfileTabState extends State<ProfileTab>
    with SingleTickerProviderStateMixin {
  bool isConnectOne = false;
  bool isRecordStory = false;
  bool isDummyTabShown = false;

  UserProfile? userProfileInfo = UserProfile();
  UserRelationship? userRelationshipWithSelf = UserRelationship();
  ProfileUserType profileUserType = ProfileUserType.self;
  ScrollController? storiesController;
  List<Tags> allTags = [];
  List<List<Video>> allTagsVideoList = [];
  List<Video> allVideos = [];
  bool isVideoLoaded = false;
  bool isVideoLoadError = false;

  TabController? _tabController;
  int selectedTapIndex = 0;
  bool isProfileTab = true;
  bool isMyFriend = false;

  List<UserProfile> requestsToYou = [];
  List<UserRelationship> requestsToYouRelationship = [];

  List<UserProfile> yourPeople = [];
  List<UserRelationship> yourPeopleRelationship = [];

  bool isProfileDataLoaded = false;
  UserProfileBloc userProfileBloc = UserProfileBloc();
  VideoApi videoApi = VideoApi();

  int profileUserId = 0;
  bool isProfileMenuLoad = false;

  double aspactRatio = 0.88;

  static ProfileTabState? sharedManager;

  static ProfileTabState getInstance() {
    return sharedManager!;
  }

  @override
  void initState() {
    sharedManager = this;
    // isProfileTab = widget.isProfileTab;
    profileUserId = widget.userId;
    profileUserType = ProfileUserType.noConnection;
    videoApi.videoLoadStatus = LoadingStatus.notStarted;

    Future.delayed(Duration.zero, () async {
      await loadTotorialData();
      await loadUserProfile();
      await getVideoList();
    });

    storiesController = ScrollController();
    _tabController = TabController(
      vsync: this,
      length: 2,
    );
    super.initState();
  }

  loadTotorialData() async {
    isRecordStory = VideoTC().isRecordStory;
    isConnectOne = VideoTC().isConnectOne;

    setState(() {});
  }

  loadUserProfile() async {
    debugPrint("profileUserId------- $profileUserId");
    if (profileUserId == -1 || profileUserId == 0) {
      profileUserId = VideoTC().currentUserProfile!.id!;
      profileUserType = ProfileUserType.self;
    }
    debugPrint("profileUserId $profileUserId");

    if (profileUserId == VideoTC().currentUserProfile!.id) {
      profileUserType = ProfileUserType.self;
      // VideoTC().currentUserProfile = userProfileInfo;
      if (VideoTC().isPeopleTabReload == true) {
        userProfileInfo = await userProfileBloc.getUserProfile(
            context, VideoTC().currentUserProfile!.id!);
        VideoTC().currentUserProfile = userProfileInfo;
        VideoTC().setUserRelationShip();
        VideoTC().isPeopleTabReload == false;
      } else {
        userProfileInfo = VideoTC().currentUserProfile;
      }
    } else {
      userProfileInfo =
          await userProfileBloc.getUserProfile(context, profileUserId);
      if (isUserMyFriend(profileUserId)) {
        setUserRelationWithSelf(profileUserId);
      } else {
        profileUserType = ProfileUserType.noConnection;
      }
    }

    if (userProfileInfo != null) {
      // userProfileInfo!.show();

      requestsToYou = [];
      requestsToYouRelationship = [];

      yourPeople = [];
      yourPeopleRelationship = [];

      for (int i = 0;
          i < userProfileInfo!.toUserRelationshipsDetails!.length;
          i++) {
        setUserProfile(userProfileInfo!.toUserRelationshipsDetails![i], true);
      }
      for (int i = 0;
          i < userProfileInfo!.fromUserRelationshipsDetails!.length;
          i++) {
        setUserProfile(
            userProfileInfo!.fromUserRelationshipsDetails![i], false);
      }
    }
    if (profileUserId == VideoTC().currentUserProfile!.id!) {
      isProfileTab = true;
    } else {
      isProfileTab = false;
    }
    if (mounted) {
      setState(() {
        isProfileDataLoaded = true;
        // Navigator.of(context).pop();
      });
    }
  }

  addInYourPeople(userProfile, userRelationship) {
    int insertIndex = 0;
    while (insertIndex < yourPeople.length) {
      UserRelationship yourPeopleRelation = yourPeopleRelationship[insertIndex];
      if (yourPeopleRelation.connection_request_status !=
          ConnectionStatus.approved.value) {
        break;
      }
      insertIndex++;
    }

    if (userRelationship.connection_request_status ==
            ConnectionStatus.pending.value ||
        userRelationship.connection_request_status ==
            ConnectionStatus.resent.value ||
        userRelationship.connection_request_status ==
            ConnectionStatus.invited.value) {
      while (insertIndex < yourPeople.length) {
        UserRelationship yourPeopleRelation =
            yourPeopleRelationship[insertIndex];
        if (yourPeopleRelation.connection_request_status !=
            ConnectionStatus.pending.value) {
          break;
        }
        insertIndex++;
      }
    }

    if (userRelationship.connection_request_status ==
            ConnectionStatus.resent.value ||
        userRelationship.connection_request_status ==
            ConnectionStatus.invited.value) {
      while (insertIndex < yourPeople.length) {
        UserRelationship yourPeopleRelation =
            yourPeopleRelationship[insertIndex];
        if (yourPeopleRelation.connection_request_status !=
            ConnectionStatus.resent.value) {
          break;
        }
        insertIndex++;
      }
    }

    if (userRelationship.connection_request_status ==
        ConnectionStatus.invited.value) {
      while (insertIndex < yourPeople.length) {
        UserRelationship yourPeopleRelation =
            yourPeopleRelationship[insertIndex];
        if (yourPeopleRelation.connection_request_status !=
            ConnectionStatus.invited.value) {
          break;
        }
        insertIndex++;
      }
    }
    yourPeople.insert(insertIndex, userProfile);
    yourPeopleRelationship.insert(insertIndex, userRelationship);
  }

  setUserProfile(UserRelationship userRelationship, bool isToUser) {
    int userType = 0;
    if (isToUser == false) {
      if (userRelationship.connection_request_status ==
              ConnectionStatus.pending.value ||
          userRelationship.connection_request_status ==
              ConnectionStatus.resent.value) {
        userType = 1;
      } else if (userRelationship.connection_request_status ==
          ConnectionStatus.invited.value) {
        userType = 2;
      }
    } else {
      if (userRelationship.connection_request_status ==
          ConnectionStatus.approved.value) {
        userType = 3;
      }
    }

    UserProfile userProfile = userProfileBloc.createUserProfile(
        userRelationship.id!,
        userRelationship.first_name!,
        userRelationship.middle_name!,
        userRelationship.last_name!,
        storyCount: userRelationship.stories,
        familyCount: userRelationship.total_family_count,
        profile_pic: userRelationship.profile_pic,
        created_at: userRelationship.created_at,
        updated_at: userRelationship.updated_at,
        userType: userType);

    if (userRelationship.connection_request_status ==
        ConnectionStatus.approved.value) {
      addInYourPeople(userProfile, userRelationship);
    } else if (userRelationship.connection_request_status ==
        ConnectionStatus.pending.value) {
      if (isToUser) {
        requestsToYou.add(userProfile);
        requestsToYouRelationship.add(userRelationship);
      } else {
        addInYourPeople(userProfile, userRelationship);
      }
    } else if (userRelationship.connection_request_status ==
        ConnectionStatus.resent.value) {
      if (isToUser) {
        requestsToYou.add(userProfile);
        requestsToYouRelationship.add(userRelationship);
      } else {
        addInYourPeople(userProfile, userRelationship);
      }
    } else if (userRelationship.connection_request_status ==
        ConnectionStatus.invited.value) {
      addInYourPeople(userProfile, userRelationship);
    }
  }

  bool isUserMyFriend(int userId) {
    bool isUserFriend = false;
    UserProfile currentUserProfile = VideoTC().currentUserProfile!;
    for (int i = 0;
        i < currentUserProfile.fromUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile.fromUserRelationshipsDetails![i];

      if (userRelationship.id == userId) {
        isUserFriend = true;
        break;
      }
    }
    if (isUserFriend == false) {
      for (int i = 0;
          i < currentUserProfile.toUserRelationshipsDetails!.length;
          i++) {
        UserRelationship userRelationship =
            currentUserProfile.toUserRelationshipsDetails![i];
        if (userRelationship.id == userId) {
          isUserFriend = true;
          break;
        }
      }
    }
    return isUserFriend;
  }

  setUserRelationWithSelf(int userId) {
    bool isToUser = false;
    UserProfile currentUserProfile = VideoTC().currentUserProfile!;

    for (int i = 0;
        i < currentUserProfile.toUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile.toUserRelationshipsDetails![i];
      if (userRelationship.id == userId) {
        userRelationshipWithSelf = userRelationship;
        isToUser = true;
        break;
      }
    }
    if (isToUser == false) {
      for (int i = 0;
          i < currentUserProfile.fromUserRelationshipsDetails!.length;
          i++) {
        UserRelationship userRelationship =
            currentUserProfile.fromUserRelationshipsDetails![i];
        if (userRelationship.id == userId) {
          userRelationshipWithSelf = userRelationship;
          isToUser = false;
        }
      }
    }

    int userType = 0;
    if (isToUser == false) {
      if (userRelationshipWithSelf!.connection_request_status ==
              ConnectionStatus.pending.value ||
          userRelationshipWithSelf!.connection_request_status ==
              ConnectionStatus.resent.value) {
        userType = 1;
      } else if (userRelationshipWithSelf!.connection_request_status ==
          ConnectionStatus.invited.value) {
        userType = 2;
      }
    } else {
      if (userRelationshipWithSelf!.connection_request_status ==
          ConnectionStatus.approved.value) {
        userType = 3;
      }
    }
    userProfileInfo!.userType = userType;

    if (userRelationshipWithSelf!.connection_request_status ==
        ConnectionStatus.approved.value) {
      profileUserType = ProfileUserType.friend;
    } else if (userRelationshipWithSelf!.connection_request_status ==
        ConnectionStatus.pending.value) {
      if (isToUser) {
        profileUserType = ProfileUserType.unApproved;
      } else {
        profileUserType = ProfileUserType.friend;
      }
    } else if (userRelationshipWithSelf!.connection_request_status ==
        ConnectionStatus.resent.value) {
      profileUserType = ProfileUserType.friend;
    } else if (userRelationshipWithSelf!.connection_request_status ==
        ConnectionStatus.invited.value) {
      profileUserType = ProfileUserType.friend;
    }
    if (userRelationshipWithSelf!.connection_request_status ==
        ConnectionStatus.approved.value) {
      isMyFriend = true;
    }
  }

  Future<void> getVideoList() async {
    allVideos = [];
    allTags = [];
    allTagsVideoList = [];
    if (videoApi.videoLoadStatus != LoadingStatus.complete) {
      if (videoApi.videoLoadStatus == LoadingStatus.loading) {
        return;
      }
      if (mounted) {
        setState(() {
          videoApi.videoLoadStatus = LoadingStatus.loading;
          isVideoLoaded = false;
        });

        allVideos = await videoApi.getVideoList(
            context,
            profileUserType == ProfileUserType.self
                ? VideoTC().currentUserProfile!.id!
                : profileUserId);
      }
      debugPrint("allVideos.length ${allVideos.length}");
      for (int i = 0; i < allVideos.length; i++) {
        setVideoInTags(allVideos[i]);
      }

      List<ObjectForSorting> allObjectForSorting = [];
      for (int i = 0; i < allTags.length; i++) {
        Tags tags = allTags[i];
        int tagIndex = getTagIndex(allTags[i]);
        ObjectForSorting objectForSorting = ObjectForSorting();
        objectForSorting.createObject(tagIndex, tags, allTagsVideoList[i]);
        allObjectForSorting.add(objectForSorting);
      }
      allObjectForSorting.sort((a, b) => a.index!.compareTo(b.index!));

      allTags = [];
      allTagsVideoList = [];
      for (int i = 0; i < allObjectForSorting.length; i++) {
        allTags.add(allObjectForSorting[i].tags!);
        allTagsVideoList.add(allObjectForSorting[i].tagsVideoList!);
      }

      videoApi.videoLoadStatus = LoadingStatus.complete;
    }
    if (mounted) {
      setState(() {
        isVideoLoaded = true;
      });
    }

    if (VideoTC().selectedNotification != null) {
      debugPrint("ProfieTab ${VideoTC().selectedNotification!.type}");
      Notifications notifications = VideoTC().selectedNotification!;
      if (notifications.type == NotificationType.comment ||
          notifications.type == NotificationType.answeredYourQuestion ||
          notifications.type == NotificationType.view ||
          notifications.type == NotificationType.like) {
        bool needToOpenComment = false;
        bool needToOpenLikeView = false;
        if (notifications.type == NotificationType.comment) {
          needToOpenComment = true;
        } else if (notifications.type == NotificationType.view) {
          needToOpenLikeView = true;
        } else if (notifications.type == NotificationType.like) {
          needToOpenLikeView = true;
        }
        for (int i = 0; i < allTagsVideoList.length; i++) {
          bool isVideoLoaded = false;
          for (int j = 0; j < allTagsVideoList[i].length; j++) {
            Video video = allTagsVideoList[i][j];

            if (video.id == notifications.video_id) {
              Future.delayed(
                Duration.zero,
                () async {
                  if (!mounted) {
                    return;
                  }
                  Navigator.pushNamed(
                    context,
                    singleViewPageRoute,
                    arguments: {
                      "video": video,
                      "needToOpenComment": needToOpenComment,
                      "parentClass": this,
                      "needToOpenLikeView": needToOpenLikeView,
                      "needToFullPop": false,
                    },
                  );
                },
              );
              isVideoLoaded = true;

              break;
            }
            if (isVideoLoaded) {
              break;
            }
          }
        }
      }
      VideoTC().selectedNotification = null;
    }
  }

  updateViewCount(Video video) {
    video.viewCount = video.viewCount! + 1;
    if (mounted) {
      setState(() {});
    }
  }

  int getTagIndex(Tags tags) {
    int tagIndex = -1;
    for (int i = 0; i < VideoTC().allTags.length; i++) {
      Tags videoTCTags = VideoTC().allTags[i];
      if (videoTCTags.id == tags.id) {
        tagIndex = i;
        break;
      }
    }
    return tagIndex;
  }

  void addInCustomTag(Video video) {
    bool isNewTag = true;
    for (int j = 0; j < allTags.length; j++) {
      Tags oldTags = allTags[j];
      if (oldTags.id == 0) {
        addVideoInTags(j, video);
        isNewTag = false;
        break;
      }
    }
    if (isNewTag == true) {
      Tags tags = Tags.createTags(0, "Uncategorized", null);
      allTags.add(tags);
      List<Video> tagVideoList = [];
      allTagsVideoList.add(tagVideoList);
      addVideoInTags(allTags.length - 1, video);
    }
  }

  void setVideoInTags(Video video) {
    if (video.question!.createdBy == null && video.question!.tags!.isNotEmpty) {
      for (int i = 0; i < video.question!.tags!.length; i++) {
        Tags tags = video.question!.tags![i];
        // debugPrint("tags ${tags.id} ${tags.title}");
        bool isNewTag = true;
        for (int j = 0; j < allTags.length; j++) {
          Tags oldTags = allTags[j];
          if (tags.id == oldTags.id) {
            addVideoInTags(j, video);
            isNewTag = false;
            break;
          }
        }
        bool isTagEnable = false;
        for (int i = 0; i < VideoTC().allTags.length; i++) {
          Tags oldTags = VideoTC().allTags[i];
          if (oldTags.id == tags.id) {
            isTagEnable = true;
            break;
          }
        }
        // debugPrint("isTagEnable $isTagEnable");
        if (isNewTag && isTagEnable == true) {
          allTags.add(tags);
          List<Video> tagVideoList = [];
          allTagsVideoList.add(tagVideoList);
          addVideoInTags(allTags.length - 1, video);
        } else if (isTagEnable == false) {
          addInCustomTag(video);
        }
      }
    } else {
      addInCustomTag(video);
    }
  }

  void addVideoInTags(int tagIndex, Video video) {
    allTagsVideoList[tagIndex].add(video);
  }

  @override
  Widget build(BuildContext context) {
    double profileFontSize = 12.0;
    bool isFromNotificationPage =
        MainPageState.getInstance()!.isFromNotificationPage;
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        backgroundColor: kWhiteColor,
        foregroundColor: kButtonColor,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(4.0),
          child: Container(
            color: Colors.grey,
            height: 1.0,
          ),
        ),
        title: isDummyTabShown == false
            ? const Image(
                image: AssetImage("assets/images/cs_logo_header.png"),
                width: 200,
                height: 28,
                fit: BoxFit.contain,
              )
            : getAppBarUserInfo(),
        centerTitle: isDummyTabShown == false ? true : false,
        leading: isProfileTab == false || isFromNotificationPage == true
            ? BackButton(
                color: Colors.black,
                onPressed: () => {
                  if (isFromNotificationPage == true)
                    {
                      MainPageState.getInstance()!.loadNotificationPage(),
                    }
                  else
                    {
                      loadUserProfileTab(VideoTC().currentUserProfile!.id!),
                    }
                }, // <-- SEE HERE
              )
            : isDummyTabShown == true
                ? getAppBarUserIcon()
                : Container(),
        actions: [
          Stack(
            alignment: Alignment.center,
            children: [
              if (profileUserType == ProfileUserType.self) ...[
                IconButton(
                  onPressed: () {
                    setState(() {
                      isProfileMenuLoad = true;
                    });
                    Navigator.of(context)
                        .pushNamed(profileMenuRoute, arguments: {
                      "parentClass": this,
                    });
                  },
                  icon: const Icon(Icons.settings_outlined),
                  color: tertiaryColor,
                ),
              ] else if (profileUserType == ProfileUserType.friend) ...[
                PopupMenuButton(
                  // padding: const EdgeInsets.only(bottom: 15),
                  icon: const Icon(Icons.more_vert),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      onTap: () async {
                        showDiscardAlert();
                      },
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: createText(
                          "Remove connection",
                          profileFontSize,
                        ),
                      ),
                    ),
                  ],
                )
              ]
            ],
          ),
        ],
      ),
      bottomSheet: getBottomSheet(),
      body: //RefreshIndicator(
          //     onRefresh: () async {
          //       setState(() {
          //         isProfileDataLoaded = false;
          //         loadUserProfile();
          //         videoApi.videoLoadStatus = LoadingStatus.notStarted;
          //         getVideoList();
          //       });
          //     },
          //     child:
          Stack(
        children: [
          getContent(),
          if (isDummyTabShown == true) ...{
            tabs(),
          }
        ],
        //),
      ),
    );
  }

  profileMenulBack() {
    if (mounted) {
      setState(() {
        isProfileMenuLoad = false;
      });
    }
  }

  Widget getContent() {
    // debugPrint("getContent ${widget.parentClass.toString()}");
    /*TextStyle titleStyle = TextStyle(
      fontFamily: getFontFamily(fontType: FontType.lora),
      fontSize: 18.0,
      fontWeight: FontWeight.bold,
    );
    double titleHeight = calculateTextHeight(
      context,
      userProfileInfo!.getFullName(),
      titleStyle,
      MediaQuery.of(context).size.width - 10,
    );*/
    return ListView(
      shrinkWrap: true,
      primary: false,
      padding: const EdgeInsets.only(bottom: 80),
      physics: const AlwaysScrollableScrollPhysics(),
      controller: storiesController,
      children: [
        getUserInfo(),
        const SizedBox(
          height: 10,
        ),
        VisibilityDetector(
          key: const Key('Cell-1'),
          onVisibilityChanged: (VisibilityInfo info) {
            if (mounted) {
              setState(() {
                if (isProfileMenuLoad == false) {
                  if (info.visibleFraction < 1) {
                    isDummyTabShown = true;
                  } else {
                    isDummyTabShown = false;
                  }
                }
              });
            }
          },
          child: tabs(),
        ),

        // tabViews(),
        if (selectedTapIndex == 0) ...[
          loadProfileStoryTab(),
        ] else ...[
          loadProfilePeopleTab(),
        ],
        const SizedBox(
          height: 100,
        ),
      ],
    );
  }

  Widget getInfoSelfButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 180,
          child: TextButton(
            onPressed: () {
              VideoTC().setQuestionTabRepient(
                  VideoTC().currentUserProfile, UserRelationship());
              if (isProfileTab == false) {
                Navigator.of(context).pop();
              }
              setBottomTabIndex.sink.add(1);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: kLightBlue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0),
              ),
            ),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: createText(
                "Add a Story",
                12,
                textColor: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(
          width: 10,
        ),
        SizedBox(
          width: 180,
          child: TextButton(
            onPressed: () async {
              callAskPermission(this, "Add a Connection");
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
                side: const BorderSide(
                  color: Color.fromARGB(255, 235, 235, 235), // your color here
                  width: 1,
                ),
              ),
            ),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: createText(
                "Add a Connection",
                11,
                textColor: const Color.fromARGB(255, 151, 151, 151),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget getInfoFriendButton(int userType) {
    if (userType == 0) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TextButton(
            onPressed: () {
              VideoTC().setQuestionTabRepient(
                  userProfileInfo, userRelationshipWithSelf);
              if (isProfileTab == false) {
                // Navigator.of(context).pop();
              }
              setBottomTabIndex.sink.add(1);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: kLightBlue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0),
              ),
            ),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: createText(
                "Send a Question",
                12,
                textColor: Colors.white,
                fontWeight: FontWeight.bold,
                fontType: FontType.lato,
              ),
            ),
          ),
        ],
      );
    } else if (userType == 1) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TextButton(
            onPressed: () {
              openViewRequestPage(
                  this,
                  userProfileInfo!,
                  userRelationshipWithSelf!,
                  userRelationshipWithSelf!.name_as_outsider!,
                  userRelationshipWithSelf!.name_as_from!,
                  Invite(),
                  RequestType.resendRequest);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0),
                side: const BorderSide(
                  color: Colors.grey,
                  width: 1,
                ),
              ),
            ),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: createText(
                "View Request",
                12,
                textColor: Colors.grey,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      );
    } else if (userType == 3) {
      // if (allVideos.isEmpty) return const SizedBox();
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TextButton(
            onPressed: () {
              VideoTC().setQuestionTabRepient(
                  userProfileInfo, userRelationshipWithSelf);
              if (isProfileTab == false) {
                // Navigator.of(context).pop();
              }
              setBottomTabIndex.sink.add(1);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: kLightBlue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0),
              ),
            ),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: createText(
                "Send a Question",
                12,
                textColor: Colors.white,
                fontWeight: FontWeight.bold,
                fontType: FontType.lato,
              ),
            ),
          ),
        ],
      );
    }
    return const SizedBox();
  }

  Widget getInfoNoFriendButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(
          height: 10,
        ),
        TextButton(
          onPressed: () {
            openRelationPage(userProfileInfo!);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: kLightBlue,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15.0),
            ),
          ),
          child: createText(
            "Connect",
            15,
            textColor: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget getInfoUnApprovedButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 30,
          width: 80,
          child: TextButton(
            onPressed: () {
              setConnectionStatus(
                  this,
                  userProfileInfo!,
                  userRelationshipWithSelf!,
                  userRelationshipWithSelf!.name_as_from!,
                  userRelationshipWithSelf!.name_as_outsider!,
                  false,
                  ConnectionStatus.approved,
                  Invite());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: kLightBlue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0),
              ),
            ),
            child: FittedBox(
              child: createText(
                "Approve",
                11,
                textColor: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(
          width: 10,
        ),
        SizedBox(
          width: 110,
          height: 30,
          child: TextButton(
            onPressed: () {
              openViewRequestPage(
                  this,
                  userProfileInfo!,
                  userRelationshipWithSelf!,
                  userRelationshipWithSelf!.name_as_from!,
                  userRelationshipWithSelf!.name_as_outsider!,
                  Invite(),
                  RequestType.approveRequest);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color.fromARGB(255, 230, 235, 250),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0),
              ),
            ),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: createText(
                "View request",
                12,
                textColor: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget getUserInfo() {
    if (allVideos.isNotEmpty) {
      isRecordStory = true;
    }
    if (requestsToYou.isNotEmpty || yourPeople.isNotEmpty) {
      isConnectOne = true;
    }

    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 16, 0, 0),
      child: Column(
        children: [
          isProfileDataLoaded == true
              ? getUserIcon(
                  userProfileInfo!.id!,
                  userProfileInfo!.getFullName(),
                  userProfileInfo!.profile_pic!,
                  size: 100,
                  radius: 50,
                  isGoProfile: false,
                  parentClass: widget.parentClass,
                )
              : getUserIcon(
                  -1,
                  "",
                  "",
                  size: 100,
                  radius: 50,
                  isGoProfile: false,
                  isDummyUser: true,
                  parentClass: widget.parentClass,
                ),
          isProfileDataLoaded == true
              ? Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: createText(
                        userProfileInfo!.getFullName(),
                        18,
                        textColor: kDarkBlue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (profileUserType == ProfileUserType.self) ...{
                      if (isConnectOne == true && isRecordStory == true) ...{
                        getInfoSelfButton(),
                      }
                    } else if (profileUserType ==
                        ProfileUserType.unApproved) ...{
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: createText(
                            "Your ${userRelationshipWithSelf!.name_as_outsider!}(Pending your approval)",
                            14.0,
                            textColor: Colors.grey,
                            fontWeight: FontWeight.w500,
                            textAlign: TextAlign.center),
                      ),
                      /*Container(
                        width: MediaQuery.of(context).size.width - 150,
                        padding: const EdgeInsets.only(left: 8.0),
                        child: createText("(Pending your approval)", 14.0,
                            textColor: Colors.grey,
                            fontWeight: FontWeight.w500,
                            textAlign: TextAlign.left),
                      ),*/
                      const SizedBox(
                        height: 5,
                      ),
                      getInfoUnApprovedButton(),
                    } else if (profileUserType == ProfileUserType.friend) ...{
                      if (userProfileInfo!.userType == 0) ...{
                        SizedBox(
                          width: MediaQuery.of(context).size.width,
                          // padding: const EdgeInsets.only(left: 8.0),
                          child: createText(
                              "Your ${userRelationshipWithSelf!.name_as_from!}",
                              14.0,
                              textColor: Colors.grey,
                              fontWeight: FontWeight.w500,
                              textAlign: TextAlign.center),
                        ),
                        getInfoFriendButton(0),
                      } else if (userProfileInfo!.userType == 1) ...{
                        Container(
                          width: MediaQuery.of(context).size.width,
                          padding: const EdgeInsets.only(left: 8.0),
                          child: createText(
                              "Your ${userRelationshipWithSelf!.name_as_from!}(Request Pending)",
                              14.0,
                              textColor: Colors.grey,
                              fontWeight: FontWeight.w500,
                              textAlign: TextAlign.center),
                        ),
                        getInfoFriendButton(1),
                      } else if (userProfileInfo!.userType == 2) ...{
                        Container(
                          width: MediaQuery.of(context).size.width - 150,
                          padding: const EdgeInsets.only(left: 8.0),
                          child: createText(
                              "Your ${userRelationshipWithSelf!.name_as_from!} (Request pending)",
                              14.0,
                              textColor: Colors.grey,
                              fontWeight: FontWeight.w500,
                              textAlign: TextAlign.left),
                        ),
                      } else if (userProfileInfo!.userType == 3) ...{
                        SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: createText(
                              "Your ${userRelationshipWithSelf!.name_as_outsider!}",
                              14.0,
                              textColor: Colors.grey,
                              fontWeight: FontWeight.w500,
                              textAlign: TextAlign.center),
                        ),
                        getInfoFriendButton(3),
                      } /*else ...{
                            Container(
                              width: MediaQuery.of(context).size.width - 150,
                              padding: const EdgeInsets.only(left: 0.0),
                              child: createText("", 14.0,
                                  textColor: Colors.grey,
                                  fontWeight: FontWeight.w500,
                                  textAlign: TextAlign.left),
                            ),
                          }*/
                    } else if (profileUserType ==
                        ProfileUserType.noConnection) ...{
                      getInfoNoFriendButton(),
                      /* Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: createText("No relation to you", 14.0,
                                textColor: Colors.grey,
                                fontWeight: FontWeight.w500,
                                textAlign: TextAlign.left),
                          ),
                          SizedBox(
                            width: 25,
                            height: 20,
                            //color: Colors.red,
                            child: IconButton(
                              onPressed: () {
                                openRelationPage(userProfileInfo!);
                              },
                              highlightColor: Colors.white,
                              icon: const Icon(
                                Icons.edit,
                                size: 20,
                              ),
                              padding: const EdgeInsets.all(0.0),
                              constraints: const BoxConstraints.expand(
                                  width: 20, height: 20),
                              // iconSize: 00,
                            ),
                          ),
                        ],
                      ),*/
                      /* Container(
                            width: MediaQuery.of(context).size.width - 150,
                            padding: const EdgeInsets.only(left: 0.0),
                            child: createText("", 14.0,
                                textColor: Colors.grey,
                                fontWeight: FontWeight.w500,
                                textAlign: TextAlign.left),
                          ),*/
                    }
                  ],
                )
              : Container(),
        ],
      ),
    );
  }

  Widget getAppBarUserIcon() {
    return Padding(
      padding: const EdgeInsets.only(left: 5.0),
      child: getUserIcon(
        userProfileInfo!.id!,
        userProfileInfo!.getFullName(),
        userProfileInfo!.profile_pic!,
        isGoProfile: false,
        size: 30,
      ),
    );
  }

  Widget getAppBarUserInfo() {
    return Stack(
      children: [
        Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: MediaQuery.of(context).size.width - 140,
                  padding: const EdgeInsets.only(left: 0.0),
                  child: createText(userProfileInfo!.getFullName(), 16.0,
                      fontWeight: FontWeight.w500,
                      textAlign: TextAlign.left,
                      textOverflow: TextOverflow.ellipsis),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget tabs() {
    return Material(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        isScrollable: false,
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorColor: kLightBlue,
        labelColor: kLightBlue,
        unselectedLabelColor: kGrey,
        labelStyle: TextStyle(
            fontFamily: getFontFamily(),
            overflow: TextOverflow.ellipsis,
            fontWeight: FontWeight.w500,
            fontSize: 18,
            color: kLightBlue),
        onTap: (index) {
          setState(() {
            selectedTapIndex = index;
          });
        },
        tabs: [
          Tab(
            height: 40,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(2, 0, 2, 0),
                child: createText(
                  isVideoLoaded == false
                      ? "Stories (—)"
                      : "Stories (${allVideos.length})",
                  15,
                  fontWeight: FontWeight.bold,
                  textColor: kLightBlue,
                ),
              ),
            ),
          ),
          Tab(
            height: 40,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(2, 0, 2, 0),
                child: createText(
                  isProfileDataLoaded == true
                      ? "Family (${requestsToYou.length + yourPeople.length})"
                      : "Family (—)",
                  15,
                  fontWeight: FontWeight.bold,
                  textColor: kLightBlue,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  loadProfileStoryTab() {
    return loadTagsView();
    // return loadVideoView();
    /* String strText = "Send a Question";
    if (isProfileDataLoaded == true && isVideoLoaded == true) {
      if (profileUserType == ProfileUserType.self) {
        if (allVideos.isEmpty) {
          strText = "📹 Add your first Story";
        } else {
          strText = "📹 Add a Story";
        }tags
      } else {
        strText = "📹 Send ${userProfileInfo!.first_name} a question";
      }
    }

    return Center(
      child: ListView(
        shrinkWrap: true,
        primary: false,
        children: [
          getFirstCell(strText, 0),
          loadVideoView(),
        ],
      ),
    );*/
  }

  Widget loadVideoView() {
    // debugPrint("allVideos ${allVideos.length}");
    /* if (isMyFriend == false && profileUserType != ProfileUserType.self) {
      return SizedBox(
        height: 400,
        child: Warning(
          label: "",
          text:
              "${userProfileInfo!.first_name}'s Stories are only viewable by their Connections",
          iconData: Icons.lock,
          imageSize: 150,
        ),
      );
    }*/
    if (isVideoLoaded == false) return loadStoriesShimmerForProfile(5);
    if (isVideoLoadError) {
      return Warning(
        label: "System Error",
        text: "Something went wrong, please try again.",
        image: "undraw_engineering_team_u99p.png",
        buttonLbl: "RETRY",
        onBtnClick: getVideoList,
      );
    }
    if (allVideos.isEmpty) {
      return CustomInfoPage(
        strImageName: "assets/images/profile-video-empty.png",
        strInfo: profileUserType == ProfileUserType.self
            ? "You don’t have any\nStories yet."
            : "${userProfileInfo!.first_name} don’t have any\nStories yet.",
        strBoldText: "Stories",
        fontSize: 20,
        fontType1Size: 22,
      );
      /*return SizedBox(
        height: 400,
        child: Warning(
          label: "No Stories",
          text: profileUserType == ProfileUserType.self
              ? "It seems that you do not have any stories at the moment."
              : "It seems that ${userProfileInfo!.first_name} does not have any stories at the moment.",
          image: "video-empty.png",
          buttonLbl: "RETRY",
          onBtnClick: getVideoList,
        ),
      );*/
    }
    return loadVideosList();
  }

  Widget loadVideosList() {
    return ListView.builder(
        shrinkWrap: true,
        itemCount: allVideos.length + 1,
        primary: false,
        physics: const ClampingScrollPhysics(),
        itemBuilder: (BuildContext context, int index) {
          if (index >= 0 && index < allVideos.length) {
            Video video = allVideos[index];
            return Padding(
              padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
              child: VideoActivity().loadVideoCell(this, video, false, true),
            );
          } else {
            return const SizedBox(
              height: 30,
            );
          }
        });
  }

  loadProfilePeopleTab() {
    if (isProfileDataLoaded == false) return loadShimmerViewForPeople(15);
    /*if (requestsToYou.isEmpty && yourPeople.isEmpty) {
      if (profileUserType == ProfileUserType.self) {
        return errorView("You don't have any family member in capsle stories",
            loadUserProfile());
      } else {
        return errorView(
            "${userProfile!.user_name!} doesn't have any family member in capsle stories",
            loadUserProfile());
      }
    }*/
    return loadProfilePeopleList();
  }

  loadProfilePeopleList() {
    int totalPeopleCount = 1;
    int requestsToYouStartIndex = -1;
    int requestIndexOffset = -1;

    if (requestsToYou.isNotEmpty) {
      requestsToYouStartIndex = totalPeopleCount;
      requestIndexOffset = 1;
      totalPeopleCount = totalPeopleCount + requestsToYou.length;
    }

    int yourPeopleStartIndex = -1;
    int yourPeopleIndexOffset = -1;
    if (yourPeople.isNotEmpty) {
      yourPeopleStartIndex = totalPeopleCount;
      yourPeopleIndexOffset = totalPeopleCount;
      totalPeopleCount = totalPeopleCount + yourPeople.length;
    }
    String strFristCellText = "👪 Add a Connection";
    bool isUserMyFriend = false;
    // debugPrint("profileUserType $profileUserType");
    // debugPrint("userProfileInfo!.userType ${userProfileInfo!.userType}");
    if (profileUserType == ProfileUserType.self) {
      if (totalPeopleCount == 1) {
        strFristCellText = "👪 Add your first Connection";
      } else {
        strFristCellText = "👪 Add a Connection";
      }
    } else if (profileUserType == ProfileUserType.noConnection) {
      strFristCellText = "👪 Connect with ${userProfileInfo!.first_name}";
    } else if (profileUserType == ProfileUserType.friend) {
      if (userProfileInfo!.userType == 0 ||
          userProfileInfo!.userType == 2 ||
          userProfileInfo!.userType == 3) {
        isUserMyFriend = true;
      } else if (userProfileInfo!.userType == 1) {
        strFristCellText = "👪 View pending request to Connect";
      }
    } else if (profileUserType == ProfileUserType.unApproved) {
      strFristCellText = "👪 View pending request to Connect";
    }
    return ListView.builder(
        shrinkWrap: true,
        itemCount: totalPeopleCount + 1,
        primary: false,
        itemBuilder: (BuildContext context, int index) {
          if (index == 0) {
            if (isUserMyFriend == false) {
              //return getFirstCell(strFristCellText, 1);
            }
            return const SizedBox(
              height: 5,
            );
          } else if (requestsToYouStartIndex != -1 &&
              index >= requestsToYouStartIndex &&
              index < (requestsToYouStartIndex + requestsToYou.length)) {
            bool showButton = false;
            if (profileUserType == ProfileUserType.self) {
              showButton = true;
            }
            return getRequestToYouCell(
              this,
              requestsToYou[index - requestIndexOffset],
              requestsToYouRelationship[index - requestIndexOffset],
              userProfileInfo!,
              showButton: showButton,
              isGoProfile: true,
              isProfileTabDesign: true,
            );
          } else if (yourPeopleStartIndex != -1 &&
              index >= yourPeopleStartIndex &&
              index < (yourPeopleStartIndex + yourPeople.length)) {
            bool showButton = false;
            if (profileUserType == ProfileUserType.self) {
              showButton = true;
            }
            return getYourPeopleCell(
              this,
              yourPeople[index - yourPeopleIndexOffset],
              yourPeopleRelationship[index - yourPeopleIndexOffset],
              userProfileInfo!,
              showButton: showButton,
              isGoProfile: true,
              isProfileTabDesign: true,
            );
          }
          return const SizedBox(
            height: 20,
          );
        });
  }

  getFirstCell(strText, index) {
    if (isProfileDataLoaded == true && isVideoLoaded == true) {
      return SizedBox(
        height: 70,
        width: MediaQuery.of(context).size.width,
        child: Row(
          children: [
            const SizedBox(
              width: 5,
            ),
            SizedBox(
              // width: double.maxFinite,
              height: 55,
              child: ElevatedButton(
                onPressed: () {
                  if (index == 0) {
                    if (isProfileDataLoaded == true) {
                      if (profileUserType == ProfileUserType.self) {
                        VideoTC().setQuestionTabRepient(
                            VideoTC().currentUserProfile, UserRelationship());
                      } else if (profileUserType ==
                          ProfileUserType.noConnection) {
                        VideoTC().setQuestionTabRepient(userProfileInfo, null);
                      } else {
                        // debugPrint("profileUserType $profileUserType");
                        VideoTC().setQuestionTabRepient(
                            userProfileInfo, userRelationshipWithSelf);
                      }
                      Future.delayed(const Duration(milliseconds: 100), () {
                        if (isProfileTab == false) {
                          Navigator.of(context).pop();
                        }
                        setBottomTabIndex.sink.add(1);
                      });
                    }
                  } else if (index == 1) {
                    if (profileUserType == ProfileUserType.self) {
                      callAskPermission(this, strText);
                    } else if (profileUserType ==
                        ProfileUserType.noConnection) {
                      openRelationPage(userProfileInfo!);
                    } else if (profileUserType == ProfileUserType.friend) {
                      openViewRequestPage(
                          this,
                          userProfileInfo!,
                          userRelationshipWithSelf!,
                          userRelationshipWithSelf!.name_as_outsider!,
                          userRelationshipWithSelf!.name_as_from!,
                          Invite(),
                          RequestType.resendRequest);
                    } else if (profileUserType == ProfileUserType.unApproved) {
                      openViewRequestPage(
                          this,
                          userProfileInfo!,
                          userRelationshipWithSelf!,
                          userRelationshipWithSelf!.name_as_from!,
                          userRelationshipWithSelf!.name_as_outsider!,
                          Invite(),
                          RequestType.approveRequest);
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                ),
                child: Row(
                  children: [
                    // index == 0
                    //     ? const Icon(size: 40, Icons.video_camera_back_rounded)
                    //     : const SizedBox(),
                    SizedBox(
                      width: MediaQuery.of(context).size.width - 90,
                      child: createText(strText, 16,
                          textColor: Colors.black,
                          textAlign: TextAlign.left,
                          fontWeight: FontWeight.w600),
                    ),
                    const Icon(Icons.navigate_next),
                  ],
                ),
              ),
            )
          ],
        ),
      );
    }

    return ListView.builder(
        shrinkWrap: true,
        itemCount: 1,
        primary: false,
        itemBuilder: (BuildContext shimmerContext, int index) {
          return Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[200]!,
            child: SizedBox(
              height: 70,
              width: MediaQuery.of(context).size.width,
              child: const Card(),
            ),
          );
        });
  }

  openRelationPage(UserProfile userProfile) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      constraints: const BoxConstraints(
        maxWidth: double.infinity,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25.0), topRight: Radius.circular(25.0)),
      ),
      backgroundColor: Colors.white,
      builder: (bc) {
        return RelationPage(
          parentClass: this,
          userProfile: userProfile,
          userProfileBloc: userProfileBloc,
        );
        /* SizedBox(
          height: MediaQuery.of(context).size.height * 0.85,
          child: RelationPage(
            parentClass: this,
            userProfile: userProfile,
            userProfileBloc: userProfileBloc,
          ),
        );*/
      },
    );
  }

  removeUserProfileFromList(userProfileToRemove) {
    for (var i = 0; i < requestsToYou.length; i++) {
      UserProfile userProfile = requestsToYou[i];
      if (userProfile.id == userProfileToRemove.id) {
        requestsToYou.removeAt(i);
        requestsToYouRelationship.removeAt(i);

        break;
      }
    }
    for (var i = 0; i < yourPeople.length; i++) {
      UserProfile userProfile = yourPeople[i];
      if (userProfile.id == userProfileToRemove.id) {
        yourPeople.removeAt(i);
        yourPeopleRelationship.removeAt(i);

        break;
      }
    }
  }

  connectionStatusDone(
    ConnectionStatus connectionStatus,
    UserProfile userProfile,
    UserRelationship userRelationship,
    Relationships relationships, {
    bool isCurrentUserDone = false,
  }) async {
    debugPrint("Profile connectionStatusDone---- $connectionStatus");
    VideoTC().connectionStatusDone(
      connectionStatus,
      userProfile,
      userRelationship,
      relationships,
    );
    if (profileUserType == ProfileUserType.self) {
      switch (connectionStatus) {
        case ConnectionStatus.pending:
          debugPrint("ConnectionStatus.pending");
          break;
        case ConnectionStatus.approved:
          debugPrint("connectionStatusDone----");
          removeUserProfileFromList(userProfile);
          addInYourPeople(userProfile, userRelationship);

          break;
        case ConnectionStatus.invited:
          debugPrint("ConnectionStatus.invited");
          break;
        case ConnectionStatus.discarded:
          removeUserProfileFromList(userProfile);
          break;
        case ConnectionStatus.resent:
          debugPrint("ConnectionStatus.resent");
          break;
        case ConnectionStatus.recover:
          debugPrint("ConnectionStatus.recover");
          break;
      }
    } else {
      switch (connectionStatus) {
        case ConnectionStatus.pending:
          debugPrint("ConnectionStatus.pending");
          userProfileInfo!.userType = 1;
          profileUserType = ProfileUserType.friend;
          userRelationshipWithSelf = userRelationship;
          userProfileInfo!.total_family_count =
              userProfileInfo!.total_family_count! + 1;

          UserRelationship userToCurrentProfile =
              userProfileBloc.createUserRelationFromProfile(
                  VideoTC().currentUserProfile!,
                  userRelationship.name_as_from!,
                  userRelationship.name_as_outsider!,
                  ConnectionStatus.pending.value);
          userProfileInfo!.fromUserRelationshipsDetails!
              .add(userToCurrentProfile);
          setUserProfile(userToCurrentProfile, false);
          if (widget.parentClass != null) {
            widget.parentClass.connectionStatusDone(
                connectionStatus, userProfile, userRelationship,
                isCurrentUserDone: true);
          }

          break;
        case ConnectionStatus.approved:
          profileUserType = ProfileUserType.friend;
          userProfile.userType = 0;

          if (widget.parentClass != null) {
            widget.parentClass.connectionStatusDone(
                connectionStatus, userProfile, userRelationship,
                isCurrentUserDone: true);
          }
          break;
        case ConnectionStatus.invited:
          debugPrint("ConnectionStatus.invited");
          break;
        case ConnectionStatus.discarded:
          profileUserType = ProfileUserType.noConnection;
          removeUserProfileFromList(VideoTC().currentUserProfile);
          if (widget.parentClass != null) {
            widget.parentClass.connectionStatusDone(
                connectionStatus, userProfile, userRelationship,
                isCurrentUserDone: true);
          }

          break;
        case ConnectionStatus.resent:
          debugPrint("ConnectionStatus.resent");
          break;
        case ConnectionStatus.recover:
          debugPrint("ConnectionStatus.recover");
          break;
      }
      debugPrint("connectionStatus $connectionStatus");
    }
    setState(() {});
  }

  void deleteComplete(video) {
    deleteCallBack(video);
  }

  deleteCallBack(video) {
    setState(() {
      allVideos.remove(video);
    });
  }

  void updateLikeCount() {
    setState(() {});
  }

  editCallBack() {
    setState(() {
      isProfileDataLoaded = false;
      loadUserProfile();
      videoApi.videoLoadStatus = LoadingStatus.notStarted;
      getVideoList();
    });
  }

  connectionStatusDoneProfile() {
    debugPrint("connectionStatusDoneProfile-------");
  }

  /// Shows a Cupertino alert dialog asking the user to confirm discarding
  /// their connection with the currently viewed user.
  ///
  /// If the user confirms, the connection is removed and the user is no longer
  /// able to view each others' Stories.
  ///
  /// If the user cancels, the dialog is simply popped.
  ///
  /// This function is called when the user taps the "Remove Connection" button.
  void showDiscardAlert() {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext buildContext) {
        return CupertinoAlertDialog(
          title: createText("Are you sure?", 18.0, fontWeight: FontWeight.bold),
          content: createText(
              "If you remove your connection with ${userProfileInfo!.first_name}, you will not be able to view each others' Stories.",
              18.0),
          actions: [
            CupertinoDialogAction(
                child: createText("Yes", 18.0, fontWeight: FontWeight.bold),
                onPressed: () {
                  Navigator.of(context).pop();
                  removeConnection();
                }),
            CupertinoDialogAction(
              child: createText("No", 18.0, fontWeight: FontWeight.bold),
              onPressed: () {
                Navigator.of(context).pop();
              },
            )
          ],
        );
      },
    );
  }

  removeConnection() async {
    bool isFromUser = false;

    UserProfile currentUserProfile = VideoTC().currentUserProfile!;

    for (int i = 0;
        i < currentUserProfile.fromUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile.fromUserRelationshipsDetails![i];
      if (userRelationship.id == profileUserId) {
        userRelationshipWithSelf = userRelationship;
        isFromUser = true;
      }
    }

    await setConnectionStatus(
        this,
        userProfileInfo!,
        userRelationshipWithSelf!,
        userRelationshipWithSelf!.name_as_from!,
        userRelationshipWithSelf!.name_as_outsider!,
        isFromUser,
        ConnectionStatus.discarded,
        Invite());
    // await connectionStatusDone(ConnectionStatus.discarded, userProfileInfo!,
    //     userRelationshipWithSelf!);
    setState(() {});
  }

  /// Builds and returns a [BottomSheet] widget based on the current
  /// state of video and profile data loading.
  ///
  /// The bottom sheet is only displayed if either no videos are loaded
  /// when `selectedTapIndex` is 0, or if both `requestsToYou` and
  /// `yourPeople` are empty when `selectedTapIndex` is 1. Otherwise, it
  /// returns an empty [SizedBox].
  ///
  /// The [BottomSheet] contains tutorial buttons for recording a story
  /// or connecting with people, depending on the [profileUserType] and
  /// the current tab index.

  Widget getBottomSheet() {
    // if (profileUserType != ProfileUserType.self) {
    //   return const SizedBox();
    // }
    if (isVideoLoaded == false || isProfileDataLoaded == false) {
      return const SizedBox();
    }
    double totalBottomSheetHeight = 100 + MediaQuery.of(context).padding.bottom;

    bool willBottomSheetLoad = false;

    if (selectedTapIndex == 0 && isVideoLoaded == true && allVideos.isEmpty) {
      willBottomSheetLoad = true;
    } else if (selectedTapIndex == 1 &&
        isProfileDataLoaded == true &&
        requestsToYou.isEmpty &&
        yourPeople.isEmpty) {
      willBottomSheetLoad = true;
    }
    if (willBottomSheetLoad == true) {
      return BottomSheet(
        backgroundColor: Colors.white,
        builder: (bottomSheetContext) {
          return LayoutBuilder(
            builder: (layoutBuilderContext, constraints) {
              double maxWidth = constraints.maxWidth;
              return Container(
                width: maxWidth,
                height: totalBottomSheetHeight,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
                margin: EdgeInsets.only(
                  left: 0,
                  bottom: 10 + MediaQuery.of(context).padding.bottom,
                ),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    profileUserType == ProfileUserType.self
                        ? selectedTapIndex == 0
                            ? getTutorialButton(
                                this,
                                "Record your first Story",
                                "📹",
                                0,
                                isRecordStory,
                                maxWidth: maxWidth,
                              )
                            : getTutorialButton(
                                this,
                                "Connect with loved ones",
                                "👪",
                                1,
                                isConnectOne,
                                isConectAllow: true,
                                maxWidth: maxWidth,
                              )
                        : selectedTapIndex == 0
                            ? InkWell(
                                onTap: () => {
                                  debugPrint("send"),
                                },
                                child: getTutorialButton(
                                  this,
                                  "Send ${userProfileInfo!.first_name} a Question",
                                  "📹",
                                  2,
                                  false,
                                  isPop: true,
                                  context: context,
                                  isRecipient: true,
                                  recipientProfile: userProfileInfo,
                                  recipientRelationship:
                                      userRelationshipWithSelf,
                                  maxWidth: maxWidth,
                                ),
                              )
                            : getTutorialButton(
                                this,
                                "Connect with loved ones",
                                "👪",
                                1,
                                isConnectOne,
                                isConectAllow: true,
                                maxWidth: maxWidth,
                              ),
                  ],
                ),
              );
            },
          );
        },
        onClosing: () {},
      );
    } else {
      return const SizedBox();
    }
  }

  loadStoriesShimmerForProfile(count) {
    return GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: aspactRatio,
        ),
        shrinkWrap: true,
        itemCount: 6,
        primary: false,
        itemBuilder: (BuildContext context, int index) {
          return Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[200]!,
            child: const Card(),
          );
        });
  }

  Widget loadTagsView() {
    if (isVideoLoaded == false) return loadStoriesShimmerForProfile(5);
    if (isMyFriend == false && profileUserType != ProfileUserType.self) {
      return Column(
        children: [
          const SizedBox(
            height: 100,
          ),
          CustomInfoPage(
            strImageName: "assets/images/lock-big.png",
            strInfo:
                "Only ${userProfileInfo!.first_name}’s family \n can view his Stories",
            imageSize: 100,
          )
        ],
      );
    }

    if (isVideoLoadError) {
      return Warning(
        label: "System Error",
        text: "Something went wrong, please try again.",
        image: "undraw_engineering_team_u99p.png",
        buttonLbl: "RETRY",
        onBtnClick: getVideoList,
      );
    }

    if (allVideos.isEmpty) {
      return CustomInfoPage(
        strImageName: "assets/images/profile-video-empty.png",
        strInfo: profileUserType == ProfileUserType.self
            ? "You don’t have any\nvideos yet."
            : "${userProfileInfo!.first_name} don’t have any\nvideos yet.",
        // strBoldText: "Stories",
        fontSize: 20,
        fontType1Size: 22,
      );
    }
    int totalItem = allTags.length ~/ 2;

    if (allTags.length % 2 != 0) {
      totalItem = totalItem + 1;
    }

    return ListView.builder(
      // gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
      //   crossAxisCount: 2,
      //   childAspectRatio: aspactRatio,
      // ),
      shrinkWrap: true,
      primary: false,
      itemCount: totalItem,
      itemBuilder: (BuildContext context, int index) {
        return Row(
          children: [
            const SizedBox(
              width: 7,
            ),
            getTagsDesign(index * 2),
            const SizedBox(
              width: 14,
            ),
            (index * 2 + 1) < allTags.length
                ? getTagsDesign(index * 2 + 1)
                : const SizedBox(),
          ],
        );
      },
    );
  }

  getTagsDesign(int index) {
    Tags tags = allTags[index];

    String strImageName_1;
    if (kDebugMode) {
      strImageName_1 =
          "assets/images/question_category/debug/question_category_${tags.id}.png";
    } else {
      strImageName_1 =
          "assets/images/question_category/release/question_category_${tags.id}.png";
    }
    // strImageName_1 =
    //     "assets/images/question_category/release/question_category_${tags.id}.png";

    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 7, 0, 7),
      child: InkWell(
        onTap: () async {
          Navigator.pushNamed(context, tagsVideoListRoute, arguments: {
            "tags": tags,
            "allVideos": allTagsVideoList[index],
          });
        },
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(20.0),
              child: Image.asset(
                strImageName_1,
                width: MediaQuery.of(context).size.width / 2 - 15,
                height: MediaQuery.of(context).size.width / 2 - 10,
              ),
            ),
            Positioned(
              top: 10,
              right: 10,
              child: SizedBox(
                width: 50,
                height: 20,
                child: createText(
                  "${allTagsVideoList[index].length}",
                  15,
                  textColor: const Color.fromARGB(255, 75, 64, 86),
                  textAlign: TextAlign.right,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ), /*Container(
          color: const Color.fromARGB(255, 227, 222, 209),
          child: Column(
            children: [
              Image.asset(
                "assets/images/question_category/question_category_${tags.id}.png",
              ),
              const SizedBox(
                height: 5,
              ),
              Center(
                child: Row(
                  children: [
                    const SizedBox(
                      width: 5,
                    ),
                    SizedBox(
                      width: 120,
                      height: 20,
                      child: createText(
                        tags.title!,
                        15,
                        textColor: const Color.fromARGB(255, 66, 58, 76),
                        textAlign: TextAlign.left,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(
                      width: 50,
                      height: 20,
                      child: createText(
                        "${allTagsVideoList[index].length}",
                        15,
                        textColor: const Color.fromARGB(255, 66, 58, 76),
                        textAlign: TextAlign.right,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),*/
      ),
    );
  }

  loadUserProfileTab(int userId) {
    if (userId == VideoTC().currentUserProfile!.id!) {
      isProfileTab = true;
    } else {
      isProfileTab = false;
    }
    isMyFriend = false;
    isVideoLoaded = false;
    isProfileDataLoaded = false;

    isProfileMenuLoad = false;
    allTags = [];
    allTagsVideoList = [];
    allVideos = [];

    profileUserId = userId;

    profileUserType = ProfileUserType.noConnection;
    selectedTapIndex = 0;
    _tabController!.index = 0;

    videoApi.videoLoadStatus = LoadingStatus.notStarted;
    if (mounted) {
      setState(() {});
    }

    Future.delayed(Duration.zero, () async {
      await loadTotorialData();
      await loadUserProfile();
      await getVideoList();
      if (mounted) {
        setState(() {});
      }
    });
  }
}

class ObjectForSorting {
  int? index;
  Tags? tags;
  List<Video>? tagsVideoList;

  createObject(int index, Tags tags, List<Video> tagsVideoList) {
    this.index = index;
    this.tags = tags;
    this.tagsVideoList = tagsVideoList;
  }
}
