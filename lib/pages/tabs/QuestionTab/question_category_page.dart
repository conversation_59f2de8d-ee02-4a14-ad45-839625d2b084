// ignore_for_file: use_build_context_synchronously, no_leading_underscores_for_local_identifiers
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:legacylock_app/data/model/notification.dart';
import 'package:legacylock_app/data/model/question_sends.dart';
import 'package:legacylock_app/data/model/questions.dart';
import 'package:legacylock_app/data/model/tags.dart';
import 'package:legacylock_app/data/model/user_profile.dart';
import 'package:legacylock_app/data/model/user_relationship.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart';
import 'package:legacylock_app/helper/consts/colors.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:legacylock_app/pages/main_page.dart';
import 'package:legacylock_app/pages/tabs/QuestionTab/question_page.dart';
import 'package:legacylock_app/pages/tabs/QuestionTab/select_recipient_page.dart';
import 'package:legacylock_app/router/route_constants.dart';

class QuestionCategoryPage extends StatefulWidget {
  const QuestionCategoryPage({
    Key? key,
  }) : super(key: key);

  @override
  State<QuestionCategoryPage> createState() => QuestionCategoryPageState();
}

class QuestionCategoryPageState extends State<QuestionCategoryPage> {
  bool isConnectOne = false;
  double aspactRatio = 0.88;
  double totalBottomSheetHeight = 0;

  bool isRecipient = false;
  bool isRecipientMySelf = false;
  bool isRequestToYou = false;
  bool isVideoSubmitted = false;

  List<QuestionSends> recipientQuestionSendList = [];
  List<QuestionSends> currentUserQuestionSendList = [];

  UserProfile? selectedProfile;
  UserRelationship? selectedRelationship;

  late QuestionPageState questionPageState;
  bool isQuestionPage = false;

  List<Questions> allQuestionList = [];

  @override
  /// Initializes the state of the QuestionCategoryPage.
  ///
  /// Sets the initial status of the question send list to `notStarted`, loads
  /// the question list and tutorial data. If the user is a recipient, it
  /// attempts to select the recipient based on the available relationship
  /// information. This function is called once when the stateful widget is
  /// inserted into the widget tree.

  void initState() {
    VideoTC().questionBloc!.questionSendListStatus = LoadingStatus.notStarted;
    loadQuestionList();
    loadTotorialData();
    Future.delayed(Duration.zero, () {
      loadQuestionSendList();
    });
    if (VideoTC().isRecipient) {
      Future.delayed(Duration.zero, () async {
        if (VideoTC().recipientRelationship != null) {
          await selectRecipient(
              VideoTC().recipientProfile!, VideoTC().recipientRelationship!);
        } else {
          await selectRecipient(VideoTC().recipientProfile!, null);
        }

        VideoTC().isRecipient = false;
      });
    }
    super.initState();
  }

  /// Loads all questions into the local question list.
  ///
  /// Clears the existing `allQuestionList` and populates it with questions
  /// from `VideoTC().allQuestionList`. Also updates the question tags count.

  loadQuestionList() {
    allQuestionList = [];
    for (int i = 0; i < VideoTC().allQuestionList.length; i++) {
      allQuestionList.add(VideoTC().allQuestionList[i]);
    }
    VideoTC().setQuestionTagsCount();
  }

  addQuestionsInQuestionList(List<Questions> allQuestions) {
    for (int i = 0; i < allQuestions.length; i++) {
      allQuestionList.add(allQuestions[i]);
    }
  }

  /// Adds a question to the local question list if it does not already exist.
  ///
  /// Checks if the question is already in `allQuestionList` and if not, adds
  /// it to the list and creates a custom question activity for it.
  addQuestionSendInQuestionList(Questions questions) {
    bool isNewQuestion = true;
    for (int i = 0; i < allQuestionList.length; i++) {
      if (allQuestionList[i].id == questions.id) {
        isNewQuestion = false;
      }
    }
    if (isNewQuestion == true) {
      VideoTC().questionBloc!.createCustomQuestionActivity(
          questions,
          QustionActivityStatus.custom.value,
          questions.createdBy,
          questions.createdBy,
          isCustomQuestion: true);
      allQuestionList.add(questions);
    }
  }

  loadTotorialData() async {
    isConnectOne = VideoTC().isConnectOne;
    isConnectOne = false;
    setState(() {});
  }

  /// Checks if the given mobile number has been invited by the current user.
  ///
  /// Checks through both the `fromUserRelationshipsDetails` and
  /// `toUserRelationshipsDetails` lists in the current user's profile to see
  /// if the given `mobileNumber` is present. If it is, the function returns true.
  /// If not, the function returns false.
  bool isContactInvited(mobileNumber) {
    UserProfile currentUserProfile = VideoTC().currentUserProfile!;
    for (int i = 0;
        i < currentUserProfile.fromUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile.fromUserRelationshipsDetails![i];
      if (userRelationship.to_phone_number == mobileNumber) {
        return true;
      }
    }

    for (int i = 0;
        i < currentUserProfile.toUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile.toUserRelationshipsDetails![i];
      if (userRelationship.to_phone_number == mobileNumber) {
        return true;
      }
    }
    if (VideoTC().currentUserProfile!.phone_number == mobileNumber) {
      return true;
    }
    return false;
  }


  /// Loads the question send list for a given tag, given in the
  /// selected notification.
  ///
  /// If the selected notification is not null and the notification type is
  /// `NotificationType.unlockNewQuestion`, then this function will navigate
  /// to the question page with the tag id provided in the notification.
  loadQuestionSendList() {
    if (VideoTC().selectedNotification != null) {
      Notifications notifications = VideoTC().selectedNotification!;
      if (notifications.type == NotificationType.unlockNewQuestion) {
        int tagId = notifications.tag_id!;
        Tags selectedTags = VideoTC().allTags[0];

        for (int i = 0; i < VideoTC().allTags.length; i++) {
          Tags tags = VideoTC().allTags[i];

          if (tags.id == tagId) {
            selectedTags = VideoTC().allTags[i];
            break;
          }
        }

        Navigator.of(context).pushNamed(questionPageRoute, arguments: {
          "parentClass": this,
          "selectedTags": selectedTags,
          "questionBloc": VideoTC().questionBloc!,
        });
      }
    }
  }

  @override
  /// Builds the question category page.
  ///
  /// This page displays a list of tags which the user can select to
  /// load the question send list for that tag. The list of tags is
  /// displayed as a horizontal list of buttons. When the user taps
  /// on a tag, the [loadQuestionSendList] method is called with the
  /// selected tag id as an argument. This method will navigate to the
  /// question page with the tag id provided in the selected tag.
  ///
  /// The page also displays a bottom sheet with a button to navigate
  /// to the question page with all tags.
  ///
  /// The page also displays a bottom sheet with a button to navigate
  /// to the question page with all tags.
  ///
  /// The page also displays a bottom sheet with a button to navigate
  /// to the question page with all tags.
  ///
  /// The page also displays a bottom sheet with a button to navigate
  /// to the question page with all tags.
  ///
  /// The page also displays a bottom sheet with a button to navigate
  /// to the question page with all tags.
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      /*appBar: AppBar(
        // systemOverlayStyle: const SystemUiOverlayStyle(
        //   statusBarColor: Colors.transparent,
        //   statusBarIconBrightness: Brightness.dark,
        //   statusBarBrightness: Brightness.light,
        // ),
        backgroundColor: Colors.white,
        foregroundColor: kButtonColor,
        elevation: 0,

        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(4.0),
          child: Container(
            color: Colors.grey,
            height: 1.0,
          ),
        ),
        title: const Image(
          image: AssetImage("assets/images/cs_logo_header.png"),
          width: 200,
          height: 28,
          fit: BoxFit.contain,
        ),
        centerTitle: true,
      ),*/
      bottomSheet: getBottomSheet(),
      body: Column(
        children: [
          loadTagList(),
          const SizedBox(
            height: 140,
          )
        ],
      ),
    );
  }

  setBottomSheetVariable(
      bool _isQuestionPage, QuestionPageState childQuestionPageState) {
    isQuestionPage = _isQuestionPage;
    questionPageState = childQuestionPageState;
  }

  Widget? bottomRequestToYou;
  Widget? bottomYourPeopleCell;

  /// Constructs and returns a BottomSheet widget.
  ///
  /// This method creates a BottomSheet with a transparent background and a 
  /// rounded border at the top. It adjusts the height based on various conditions 
  /// such as whether a video is submitted, whether the user is a recipient, or 
  /// whether it is a question page. The BottomSheet can display different content 
  /// based on the state of the application, such as a submitted video view, 
  /// recipient selection view, or a tutorial button to connect with loved ones.
  ///
  /// The total height of the BottomSheet is dynamically calculated using 
  /// conditions, and the content layout is managed using a Column widget.

  Widget getBottomSheet() {
    return BottomSheet(
      backgroundColor: Colors.transparent,
      onClosing: () {},
      builder: (bottomSheetContext) {
        return LayoutBuilder(
          builder: (layoutBuilderContext, constraints) {
            double maxWidth = constraints.maxWidth;

            if (isVideoSubmitted) {
              totalBottomSheetHeight = 110;
            } else {
              if (isRecipient == false) {
                totalBottomSheetHeight = 110;
              } else {
                totalBottomSheetHeight = 40;

                if (selectedRelationship != null) {
                  if (isRequestToYou == true) {
                    bottomRequestToYou = getRequestToYouCell(
                      this,
                      selectedProfile!,
                      selectedRelationship!,
                      VideoTC().currentUserProfile!,
                      showButton: false,
                      isGoProfile: false,
                      xOffSet: 80,
                      isBorder: false,
                      maxWidth: maxWidth,
                    );
                    totalBottomSheetHeight =
                        totalBottomSheetHeight + totalRequestToyouHeight;
                  } else {
                    bottomYourPeopleCell = getYourPeopleCell(
                      this,
                      selectedProfile!,
                      selectedRelationship!,
                      VideoTC().currentUserProfile!,
                      showButton: false,
                      isGoProfile: false,
                      xOffSet: 90,
                      isBorder: false,
                      maxWidth: maxWidth,
                    );
                    totalBottomSheetHeight =
                        totalBottomSheetHeight + totalYourPeopleHeight;
                  }
                }
              }
            }

            if (isQuestionPage) {
              totalBottomSheetHeight = totalBottomSheetHeight - 40;
            }
            totalBottomSheetHeight = totalBottomSheetHeight +
                50 +
                MediaQuery.of(context).padding.bottom;

            return Container(
              width: maxWidth, // Ensure full width
              height: totalBottomSheetHeight,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black54,
                    blurRadius: 20,
                    offset: Offset(0, -2),
                    spreadRadius: 0.1,
                    blurStyle: BlurStyle.normal,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  isVideoSubmitted
                      ? showVideoSubmitted(maxWidth)
                      : VideoTC().allYourPeople.isNotEmpty
                          ? isRecipient
                              ? selectRecipientView(maxWidth)
                              : noRecipient(maxWidth)
                          : Column(
                              children: [
                                const SizedBox(height: 10),
                                getTutorialButton(
                                  this,
                                  "Connect with loved ones",
                                  "👪",
                                  1,
                                  isConnectOne,
                                  isConectAllow: true,
                                  maxWidth: maxWidth,
                                ),
                              ],
                            ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /*Widget getBottomSheet() {
    if (isVideoSubmitted) {
      totalBottomSheetHeight = 110;
    } else {
      if (isRecipient == false) {
        totalBottomSheetHeight = 110;
      } else {
        totalBottomSheetHeight = 40;

        if (selectedRelationship != null) {
          if (isRequestToYou == true) {
            bottomRequestToYou = getRequestToYouCell(
              this,
              selectedProfile!,
              selectedRelationship!,
              VideoTC().currentUserProfile!,
              showButton: false,
              isGoProfile: false,
              xOffSet: 80,
              isBorder: false,
            );
            totalBottomSheetHeight =
                totalBottomSheetHeight + totalRequestToyouHeight;
          } else {
            bottomYourPeopleCell = getYourPeopleCell(
              this,
              selectedProfile!,
              selectedRelationship!,
              VideoTC().currentUserProfile!,
              showButton: false,
              isGoProfile: false,
              xOffSet: 90,
              isBorder: false,
            );
            totalBottomSheetHeight =
                totalBottomSheetHeight + totalYourPeopleHeight;
          }
        }
      }
    }
    if (isQuestionPage) {
      totalBottomSheetHeight = totalBottomSheetHeight - 40;
    }
    totalBottomSheetHeight =
        totalBottomSheetHeight + 25 + MediaQuery.of(context).padding.bottom;

    return BottomSheet(
      backgroundColor: Colors.white,
      constraints: const BoxConstraints(
        maxWidth: double.infinity,
      ),
      builder: (bottomSheetContext) => Container(
        width: MediaQuery.of(context).size.width,
        height: totalBottomSheetHeight,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black54,
              blurRadius: 20,
              offset: Offset(0, -2),
              spreadRadius: 0.1,
              blurStyle: BlurStyle.normal,
            ),
          ],
        ),
        // margin: EdgeInsets.only(
        //   left: 0,
        //   bottom: MediaQuery.of(context).padding.bottom,
        // ),

        child: Column(
          children: [
            isVideoSubmitted == true
                ? showVideoSubmitted()
                : VideoTC().allYourPeople.isNotEmpty
                    ? isRecipient == false
                        ? noRecipient()
                        : selectRecipientView()
                    : Column(
                        children: [
                          const SizedBox(
                            height: 10,
                          ),
                          getTutorialButton(this, "Connect with loved ones",
                              "👪", 1, isConnectOne,
                              isConectAllow: true),
                        ],
                      ),
          ],
        ),
      ),
      onClosing: () {},
    );
  }*/

  /// Returns a widget that shows a video has been submitted.
  ///
  /// When tapped, if [isQuestionPage] is true, pops the question page.
  /// Sets [isVideoSubmitted] to false and sets [bottomTabIndex] to 3.
  ///
  Widget showVideoSubmitted(double maxWidth) {
    return InkWell(
      onTap: () async {
        setState(() {
          isVideoSubmitted = false;
        });
        if (isQuestionPage) {
          Navigator.of(context).pop();
        }

        setBottomTabIndex.sink.add(3);
      },
      child: Column(
        children: [
          const SizedBox(
            height: 30,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: maxWidth - 160,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: createText(
                      "Story submitted!",
                      16.0,
                      textColor: Colors.grey,
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
              ),
              const SizedBox(
                width: 30,
              ),
              SizedBox(
                width: 100,
                child: Align(
                  alignment: Alignment.centerRight,
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: createText(
                      "View",
                      16.0,
                      textColor: Colors.grey,
                      textAlign: TextAlign.left,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Returns a widget that shows no recipient has been selected.
  ///
  /// When tapped, calls [openSelectRecipient] with null argument.
  ///
  /// The widget displays a grey CircleAvatar with a person icon,
  /// a text "No recipient selected" and an expand more icon.
  ///
  Widget noRecipient(double maxWidth) {
    return InkWell(
      onTap: () async {
        openSelectRecipient(null);
      },
      child: Column(
        children: [
          const SizedBox(
            height: 15,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircleAvatar(
                radius: 20,
                backgroundColor: Colors.grey,
                child: Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 35,
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              SizedBox(
                width: maxWidth - 140,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: createText(
                      "No recipient selected",
                      18.0,
                      textColor: Colors.grey,
                      textAlign: TextAlign.left,
                      fontType: FontType.lato,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ),
              const Icon(
                size: 55,
                Icons.expand_more,
                color: Colors.grey,
              ),
            ],
          ),
        ],
      ),
    );
  }

  openSelectRecipient(Questions? questions) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      // shape: RoundedRectangleBorder(
      //   borderRadius: BorderRadius.circular(15.0),
      // ),
      constraints: const BoxConstraints(
        maxWidth: double.infinity,
      ),
      backgroundColor: Colors.transparent,
      builder: (bc) {
        return Container(
          width: MediaQuery.of(context).size.width,
          height: isQuestionPage == false
              ? questions == null
                  ? 670 + MediaQuery.of(context).padding.bottom
                  : 780
              : questions == null
                  ? 620
                  : 740,
          color: Colors.transparent,
          // margin: EdgeInsets.only(
          //   left: 0,
          //   bottom: questions == null
          //       ? MediaQuery.of(context).padding.bottom
          //       : MediaQuery.of(context).padding.bottom,
          // ),
          child: Column(
            children: [
              questions != null
                  ? Padding(
                      padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                      child: Container(
                        // width: MediaQuery.of(context).size.width - 20,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(
                              color: Colors.grey,
                              width: 1.0,
                              style: BorderStyle.solid),
                          borderRadius: const BorderRadius.all(
                            Radius.circular(20),
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(15),
                          child: SizedBox(
                            width: double.maxFinite,
                            child: Align(
                              alignment: Alignment.center,
                              child: createText(
                                questions!.question_text!,
                                16,
                                fontWeight: FontWeight.bold,
                                textAlign: TextAlign.left,
                                textColor: kLightBlue,
                                maxLines: 4,
                                textOverflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ),
                      ),
                    )
                  : const SizedBox(),
              const SizedBox(
                height: 10,
              ),
              SizedBox(
                height: 600,
                child: SelectRecipientPage(
                  parentClass: this,
                  questions: questions,
                  requestsToYou: VideoTC().allRequestsToYou,
                  requestsToYouRelationship:
                      VideoTC().allRequestsToYouRelationship,
                  yourPeople: VideoTC().allYourPeople,
                  yourPeopleRelationship: VideoTC().allYourPeopleRelationship,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  setRecipientToSend(
      UserProfile userProfile, UserRelationship? userRelationship) {
    selectedProfile = userProfile;
    selectedRelationship = userRelationship;
  }

  Future<void> selectRecipient(
      UserProfile userProfile, UserRelationship? userRelationship,
      {bool isFromRequestToYou = false, bool isInvite = false}) async {
    debugPrint("isInvite $isInvite");
    /*innerAuthLoader(context);
    if (currentUserQuestionSendList.isEmpty) {
      await VideoTC().questionBloc!.getQuestionSendList(
          this, VideoTC().currentUserProfile!.id!,
          isLoading: false);
    }*/
    isRecipient = true;
    isRequestToYou = isFromRequestToYou;
    setRecipientToSend(userProfile, userRelationship);
    for (int i = 0; i < VideoTC().allTags.length; i++) {
      Tags tags = VideoTC().allTags[i];
      tags.questionSendCount = 0;
    }

    if (isInvite == false) {
      if (VideoTC().currentUserProfile!.id == userProfile.id) {
        isRecipientMySelf = true;
      }

      setState(() {});
      if (isQuestionPage) {
        questionPageState.selectRecpientStatus();
      }

      await VideoTC()
          .questionBloc!
          .getQuestionSendList(this, userProfile.id!, isLoading: true);
    } else {
      await VideoTC().questionBloc!.getQuestionSendList(
          this, userProfile.invitation_id!,
          isInvited: true, isLoading: true);
    }
    //Navigator.of(context).pop();
  }

  deselectRecipient() async {
    isRecipient = false;
    isRecipientMySelf = false;
    isRequestToYou = false;
    loadQuestionList();
    if (currentUserQuestionSendList.isEmpty) {
      await VideoTC().questionBloc!.getQuestionSendList(
          this, VideoTC().currentUserProfile!.id!,
          isLoading: true);
      /*recipientQuestionSendList.clear();
      currentUserQuestionSendList.clear();
      for (int i = 0; i < VideoTC().allInboxQuestionList.length; i++) {
        QuestionSends questionSends = VideoTC().allInboxQuestionList[i];
        recipientQuestionSendList.add(questionSends);
        currentUserQuestionSendList.add(questionSends);
      }*/
    } else {
      recipientQuestionSendList = [];
      recipientQuestionSendList.addAll(currentUserQuestionSendList);
      recipientQuestionListLoaded();
    }

    setState(() {});
    if (isQuestionPage) {
      questionPageState.deselectRecipient();
    }
  }

  Widget selectRecipientView(double maxWidth) {
    return Column(
      children: [
        const SizedBox(
          height: 5,
        ),
        Row(
          //crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (selectedRelationship != null) ...{
              if (isRequestToYou == true) ...{
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: bottomRequestToYou,
                ),
              } else ...{
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  child: bottomYourPeopleCell,
                ),
              }
            },
            Padding(
              padding: const EdgeInsets.only(bottom: 30),
              child: IconButton(
                onPressed: () async {
                  await deselectRecipient();
                },
                highlightColor: Colors.white,
                icon: const Icon(
                  Icons.close,
                  size: 50,
                ),
                padding: const EdgeInsets.all(0.0),
                constraints: const BoxConstraints.expand(width: 20, height: 20),
                // iconSize: 00,
              ),
            )
          ],
        ),
      ],
    );
  }

  Widget loadTagList() {
    /*debugPrint("VideoTC().question ${VideoTC().allTags.length}");
    for (int i = 0; i < VideoTC().allTags.length; i++) {
      Tags tags = VideoTC().allTags[i];
      debugPrint("tags ${tags.id} ${tags.title}");
    }*/
    int totalItem = VideoTC().allTags.length ~/ 2;
    //debugPrint("allTags.length ${VideoTC().allTags.length}");
    if (VideoTC().allTags.length % 2 != 0) {
      totalItem = totalItem + 1;
    }
    //debugPrint("totalItem $totalItem");

    return Expanded(
      child: ListView.builder(
        shrinkWrap: true,
        // gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        //   crossAxisCount: 2,
        //   childAspectRatio: aspactRatio,
        // ),
        itemCount: totalItem,
        itemBuilder: (BuildContext context, int index) {
          // Tags tags_1 = VideoTC().allTags[index * 2];
          // Tags tags_2 = VideoTC().allTags[index * 2 + 1];

          return Row(
            children: [
              const SizedBox(
                width: 7,
              ),
              getTagsDesign(VideoTC().allTags[index * 2]),
              const SizedBox(
                width: 14,
              ),
              (index * 2 + 1) < VideoTC().allTags.length
                  ? getTagsDesign(VideoTC().allTags[index * 2 + 1])
                  : const SizedBox(),
            ],
          );
        },
      ),
    );
  }

  getTagsDesign(Tags tags) {
    String strImageName;
    if (kDebugMode) {
      strImageName =
          "assets/images/question_category/debug/question_category_${tags.id}.png";
    } else {
      strImageName =
          "assets/images/question_category/release/question_category_${tags.id}.png";
    }
    // strImageName =
    //     "assets/images/question_category/release/question_category_${tags.id}.png";

    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 7, 0, 7),
      child: InkWell(
        onTap: () async {
          Navigator.of(context).pushNamed(questionPageRoute, arguments: {
            "parentClass": this,
            "selectedTags": tags,
            "questionBloc": VideoTC().questionBloc!,
          });
        },
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(20.0),
              child: Image.asset(
                strImageName,
                width: MediaQuery.of(context).size.width / 2 - 15,
                height: MediaQuery.of(context).size.width / 2 - 10,
              ),
            ),
            // createText(tags.title!, 12.0),
            Positioned(
              top: 10,
              right: 10,
              child: Container(
                width: 100,
                height: 20,
                alignment: Alignment.centerRight,
                child: isRecipient == true
                    ? FittedBox(
                        fit: BoxFit.scaleDown,
                        child: createText(
                          "${tags.questionSendCount}/${tags.questionCount}",
                          15,
                          textColor: const Color.fromARGB(255, 75, 64, 86),
                          textAlign: TextAlign.right,
                          fontWeight: FontWeight.w700,
                        ),
                      )
                    : const SizedBox(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  sendQuestion(questions) {
    if (isQuestionPage) {
      questionPageState.sendQuestion(questions);
    }
    debugPrint("sendQuestion-------");
    // showQuestionSentAlert(questions);
  }

  void recipientQuestionListLoaded() {
    debugPrint(
        "recipientQuestionSendList.length ${recipientQuestionSendList.length}");
    debugPrint(
        "currentUserQuestionSendList.length ${currentUserQuestionSendList.length}");

    List<Questions> questionDoneList = [];
    for (int i = 0; i < recipientQuestionSendList.length; i++) {
      QuestionSends questionSends = recipientQuestionSendList[i];
      // debugPrint("--------------");
      // debugPrint(
      //     "questionSends ${questionSends.id} ${questionSends.question!.id} ${questionSends.question!.question_text}");
      addQuestionSendInQuestionList(questionSends.question!);

      if (questionSends.video != null) {
        bool isNewQuestion = true;
        for (int i = 0; i < questionDoneList.length; i++) {
          Questions questionsDone = questionDoneList[i];
          if (questionsDone.id == questionSends.question!.id) {
            isNewQuestion = false;
            break;
          }
        }
        if (isNewQuestion) {
          questionDoneList.add(questionSends.question!);

          if (questionSends.question!.tags!.isNotEmpty) {
            List<int> allTagsIndex =
                getTagsForQuestion(questionSends.question_id!);

            for (int i = 0; i < allTagsIndex.length; i++) {
              Tags tags = VideoTC().allTags[allTagsIndex[i]];
              tags.questionSendCount = tags.questionSendCount + 1;
            }
          } else {
            if (questionSends.question!.createdBy == null) {
              Tags tags = VideoTC().allTags[VideoTC().allTags.length - 1];
              tags.questionSendCount = tags.questionSendCount + 1;
            }
          }
        }
      }
    }

    for (int i = 0; i < VideoTC().allTags.length; i++) {
      Tags tags = VideoTC().allTags[i];

      // if (tags.id == 0)
      {
        tags.questionCount =
            VideoTC().getTagQuestionCount(tags.id!, allQuestionList);
        // debugPrint("tags.questionCount ${tags.questionCount}");
      }
    }
    /*for (int i = 0; i < allQuestionList.length; i++) {
      Questions questions = allQuestionList[i];
      if (questions.tags![0].id == 16) {
        debugPrint("questions ${questions.tags![0].id}");
        debugPrint("questions ${questions.tags![0].title}");
      }
    }*/
    if (mounted) {
      setState(() {});
    }
    if (isQuestionPage) {
      if (questionPageState.mounted) {
        questionPageState.setState(() {});
      }
    }
  }

  List<int> getTagsForQuestion(int questionId) {
    List<int> tagIndexList = [];
    for (int i = 0; i < allQuestionList.length; i++) {
      Questions questions = allQuestionList[i];

      if (questions.id == questionId) {
        List<Tags> allTags = [];
        if (questions.createdBy != null) {
          allTags.add(Tags.createTags(0, "Uncategorized", null));
        } else {
          allTags.addAll(questions.tags!);
        }

        for (int j = 0; j < allTags.length; j++) {
          Tags tags = allTags[j];

          tagIndexList.add(getTagIndex(tags));
        }
      }
    }
    return tagIndexList;
  }

  int getTagIndex(Tags tags) {
    for (int i = 0; i < VideoTC().allTags.length; i++) {
      Tags tagsFromMain = VideoTC().allTags[i];
      if (tags.id == tagsFromMain.id) {
        return i;
      }
    }
    return VideoTC().allTags.length - 1;
  }
}
