import 'package:flutter/material.dart';
import 'package:legacylock_app/components/bottom_tab_bar.dart';
import 'package:legacylock_app/data/model/notification.dart';
import 'package:legacylock_app/data/model/question_sends.dart';
import 'package:legacylock_app/data/model/user_profile.dart';
import 'package:legacylock_app/data/model/user_relationship.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart';
import 'package:legacylock_app/helper/consts/colors.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:legacylock_app/pages/tabs/HomeTab/home_tab.dart';
import 'package:legacylock_app/pages/tabs/HomeTab/inbox_modal.dart';
import 'package:legacylock_app/pages/tabs/PeopleTab/people_tab.dart';
import 'package:legacylock_app/pages/tabs/ProfileTab/profile_tab.dart';
import 'package:legacylock_app/pages/tabs/QuestionTab/question_category_page.dart';
import 'package:rxdart/rxdart.dart';

final BehaviorSubject<int> setBottomTabIndex = BehaviorSubject<int>();
final BehaviorSubject<int> setBottomTabIndexChange = BehaviorSubject<int>();
final BehaviorSubject<Notifications> setGoToNotifications =
    BehaviorSubject<Notifications>();

GlobalKey keyBottomItem1 = GlobalKey();
GlobalKey keyBottomItem2 = GlobalKey();
GlobalKey keyBottomItem3 = GlobalKey();
GlobalKey keyBottomItem4 = GlobalKey();

class MainPage extends StatefulWidget {
  const MainPage({Key? key}) : super(key: key);

  @override
  State<MainPage> createState() => MainPageState();
}

class MainPageState extends State<MainPage>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  bool isBottomNotificationEnable = false;
  bool isNotificationQustionUnlock = false;

  int notificationCount = 0;
  int homeTabIndex = -1;

  List<QuestionSends> allInboxQuestionList = [];
  List<QuestionSends> allInboxQuestionNoFriendList = [];
  List<QuestionSends> allInboxAnsweredList = [];

  // ignore: prefer_typing_uninitialized_variables
  var profileParentClass;
  int profileUserId = -1;
  bool isProfileTab = true;
  bool isFromNotificationPage = false;
  bool isProfileChange = true;

  static MainPageState? sharedManager;

  static MainPageState? getInstance() {
    return sharedManager;
  }

  @override
  /// Initialize the state of the main page.
  ///
  /// Set the [TabController] with 4 tabs and the current index of the tab bar.
  /// Check if the user clicked on a push notification and navigate to the
  /// corresponding page.
  ///
  /// Set the listener for the notifications and load the inbox questions.
  ///
  void initState() {
    sharedManager = this;
    homeTabIndex = -1;
    _tabController = TabController(
      vsync: this,
      length: 4,
    );

    isBottomNotificationEnable = false;
    VideoTC().setUserRelationShip();

    setIndex();
    debugPrint("VideoTC().isPushClicked ${VideoTC().isPushClicked}");
    if (VideoTC().isPushClicked == false) {
      setInboxQuestionList();
    } else {
      homeTabIndex = 3;
      VideoTC().isPushClicked = false;
    }

    Future.delayed(Duration.zero, () async {
      setNotificationListenter();
    });

    super.initState();
  }

  @override
  /// Builds the main page of the application.
  ///
  /// This widget uses a [Scaffold] to create the structure of the page with a white
  /// background. The body contains a [Stack] with a [TabBarView] and a [BottomTabBar].
  /// 
  /// The [TabBarView] holds four tabs: [HomeTab], [QuestionCategoryPage], [PeopleTab],
  /// and [ProfileTab]. The [HomeTab] displays notifications and uses the [notificationCount]
  /// and [homeTabIndex] for its configuration. The [ProfileTab] uses [profileUserId],
  /// [isProfileTab], and [profileParentClass] for its setup.
  ///
  /// The [BottomTabBar] is aligned to the bottom center and is controlled by the same
  /// [TabController] as the [TabBarView]. It has options to enable notifications and
  /// unlock questions using [isBottomNotificationEnable] and [isNotificationQustionUnlock].
  ///
  /// The [TabBarView] is non-scrollable to enforce static tab content.

  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kWhiteColor,
      resizeToAvoidBottomInset: true,
      body: Stack(
        children: [
          TabBarView(
            controller: _tabController!,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              HomeTab(
                notificationCount: notificationCount,
                tabIndex: homeTabIndex,
              ),
              const QuestionCategoryPage(),
              const PeopleTab(),
              ProfileTab(
                userId: profileUserId,
                isProfileTab: isProfileTab,
                parentClass: profileParentClass,
              ),
            ],
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: BottomTabBar(
              tabController: _tabController!,
              isNotificationEnable: isBottomNotificationEnable,
              isNotificationQustionUnlock: isNotificationQustionUnlock,
            ),
          ),
        ],
      ),
    );
  }

  /// This function is used to load the profile tab.
  ///
  /// This function takes four parameters, varProfileParentClass, varProfileUserId,
  /// varIsProfileTab and isFromNotification. The first parameter is the parent
  /// class of the profile tab, the second parameter is the user id of the profile
  /// tab, the third parameter is a boolean value indicating whether the profile
  /// tab is shown or not, and the fourth parameter is a boolean value indicating
  /// whether the profile tab is loaded from the notification page or not.
  ///
  /// If the profile tab is already loaded, this function will not do anything.
  ///
  /// After loading the profile tab, this function will set the index of the tab
  /// controller to 3 and set the profileUserId to -1. It will also set
  /// isProfileChange to true.
  void loadProfileTab(
      var varProfileParentClass, int varProfileUserId, bool varIsProfileTab,
      {bool isFromNotification = false}) {
    if (isProfileChange == false) {
      return;
    }
    profileParentClass = varProfileParentClass;
    profileUserId = varProfileUserId;
    isProfileTab = varIsProfileTab;
    isFromNotificationPage = isFromNotification;
    debugPrint("this.profileUserId $profileUserId");
    setState(() {});
    isProfileChange = false;

    Future.delayed(const Duration(milliseconds: 100), () {
      if (_tabController!.index == 3) {
        ProfileTabState.getInstance().loadUserProfileTab(profileUserId);
      } else {
        _tabController!.animateTo(3);
      }
      profileParentClass = null;
      profileUserId = -1;
      isProfileTab = true;
      isProfileChange = true;
      setState(() {});
    });
  }

  loadNotificationPushClick() {
    VideoTC().isPushClicked = false;

    if (_tabController!.index == 0) {
      HomeTabState.getInstance().loadNotificationPage();
    } else {
      setState(() {
        homeTabIndex = 3;
      });

      Future.delayed(const Duration(milliseconds: 100), () {
        setBottomTabIndex.sink.add(0);
      });
    }
  }

  loadNotificationPage() {
    if (isFromNotificationPage == true) {
      setState(() {
        homeTabIndex = 3;
      });
    }
    Future.delayed(const Duration(milliseconds: 100), () {
      setBottomTabIndex.sink.add(0);
    });
    isFromNotificationPage = false;
  }

  setIndex() {
    setBottomTabIndex.listen((value) {
      // debugPrint("value $value");
      if (value == 6) {
        setState(() {});
      } else if (_tabController != null && value != 5) {
        _tabController!.animateTo(value);
      } else {
        setState(() {
          _tabController!.animateTo(2);
        });
      }
    });

    setBottomTabIndexChange.listen((index) {
      // debugPrint("index $index");
      // debugPrint("isBottomNotificationEnable $isBottomNotificationEnable");
      if (index == 100) {
        _tabController!.animateTo(0);
        setState(() {
          homeTabIndex = 2;
        });
      } else {
        if (index == 0 && isBottomNotificationEnable == true) {
          if (mounted) {
            setState(() {
              homeTabIndex = 0;
              isBottomNotificationEnable = false;
            });
          }
        }
        if (index == 0) {
          HomeTabState.getInstance().resetHomeTab();
        }
      }
    });
  }

  @override
  void dispose() {
    _tabController!.dispose();
    super.dispose();
  }

  setNotificationListenter() {
    setGoToNotifications.listen((Notifications? notifications) {
      debugPrint("notifications!.type ${notifications!.type}");

      VideoTC().selectedNotification = null;
      switch (notifications.type) {
        case NotificationType.comment:
        case NotificationType.view:
        case NotificationType.like:
          {
            VideoTC().selectedNotification = notifications;
            setBottomTabIndex.sink.add(3);
          }
          break;

        case NotificationType.sentMeQuestion:
          {
            VideoTC().selectedNotification = notifications;
            HomeTabState.getInstance().loadNotification();
          }
          break;
        case NotificationType.answeredYourQuestion:
          {
            VideoTC().selectedNotification = notifications;
            // setBottomTabIndex.sink.add(3);
            loadProfileTab(
              this,
              notifications.to_user_id!,
              true,
            );
          }
          break;
        case NotificationType.acceptedMyInvitation:
        case NotificationType.acceptedRequestByMe:
        case NotificationType.acceptedRequestBySomeone:
        case NotificationType.invitationAccepted:
          {
            bool isRecipient = false;
            UserProfile? recipientProfile;
            UserRelationship? recipientRelationship;
            UserProfile currentUserProfile = VideoTC().currentUserProfile!;
            for (int i = 0;
                i < currentUserProfile.toUserRelationshipsDetails!.length;
                i++) {
              UserRelationship userRelationship =
                  currentUserProfile.toUserRelationshipsDetails![i];
              if (userRelationship.id == notifications.from_user_id) {
                recipientProfile =
                    VideoTC().getUserProfile(userRelationship, true);
                recipientRelationship = userRelationship;
                isRecipient = true;
                break;
              }
            }
            if (isRecipient == false) {
              for (int i = 0;
                  i < currentUserProfile.fromUserRelationshipsDetails!.length;
                  i++) {
                UserRelationship userRelationship =
                    currentUserProfile.fromUserRelationshipsDetails![i];
                if (userRelationship.id == notifications.from_user_id) {
                  recipientProfile =
                      VideoTC().getUserProfile(userRelationship, true);
                  recipientRelationship = userRelationship;
                  isRecipient = true;
                }
              }
            }

            if (isRecipient == true) {
              VideoTC().setQuestionTabRepient(
                  recipientProfile, recipientRelationship);
              VideoTC().selectedNotification = notifications;
            }
            setBottomTabIndex.sink.add(1);
          }
          break;
        case NotificationType.requestedToConnectWithMe:
          {
            debugPrint("NotificationType.requestedToConnectWithMe-----");
            VideoTC().selectedNotification = notifications;
            HomeTabState.getInstance().loadNotification();
          }
          break;
        case NotificationType.requestedToConnectWithSomeone:
        case NotificationType.discardedRequestToConnect:
          {
            VideoTC().selectedNotification = notifications;
            setBottomTabIndex.sink.add(2);
          }
          break;
        case NotificationType.unlockNewQuestion:
          {
            VideoTC().selectedNotification = notifications;
            setBottomTabIndex.sink.add(1);
          }

        default:
          break;
      }
    });
  }


  /// This function is used to set the inbox question list. It will go through
  /// the VideoTC().allInboxQuestionList and separate the questions into three
  /// lists: allInboxQuestionList, allInboxQuestionNoFriendList and
  /// allInboxAnsweredList. The allInboxQuestionList contains the questions
  /// sent from the user's friends, the allInboxQuestionNoFriendList contains
  /// the questions sent from the user's non-friends, and the
  /// allInboxAnsweredList contains the questions that have been answered.
  /// After separating the questions, it will check if the user has any
  /// unanswered questions and if so, it will open the inbox modal after
  /// a delay of 100 milliseconds.
  
  setInboxQuestionList() {
    allInboxQuestionNoFriendList = [];
    allInboxQuestionList = [];
    allInboxAnsweredList = [];

    for (int i = 0; i < VideoTC().allInboxQuestionList.length; i++) {
      QuestionSends questionSends = VideoTC().allInboxQuestionList[i];
      // debugPrint("questionSends.id ${questionSends.id}");
      // debugPrint("questionSends.video_id ${questionSends.video_id}");
      // debugPrint("questionSends.video ${questionSends.video}");
      if (questionSends.video_id == 0 && questionSends.trashed_at == "null") {
        if (questionSends.sent_from_user != null) {
          if (VideoTC().isRequestSentToMe(questionSends.sent_from_user!)) {
            if (VideoTC().isMyFriend(questionSends.sent_from_user!)) {
              allInboxQuestionList.add(questionSends);
            } else {
              allInboxQuestionNoFriendList.add(questionSends);
            }
          } else {
            if (VideoTC().isMyFriend(questionSends.sent_from_user!)) {
              allInboxQuestionList.add(questionSends);
            } else {
              allInboxQuestionNoFriendList.add(questionSends);
            }
          }
        } else {}
      } else {
        allInboxAnsweredList.add(questionSends);
      }
    }

    int totalUnAnswerItem = VideoTC().allRequestsToYou.length +
        allInboxQuestionList.length +
        allInboxQuestionNoFriendList.length;
    if (totalUnAnswerItem > 0) {
      Future.delayed(const Duration(milliseconds: 100), () {
        openInboxModal();
      });
    }
  }

  /// Opens a modal bottom sheet displaying the inbox.
  ///
  /// The modal is scrollable, dismissible, and has a rounded top border.
  /// It displays the `InboxModal` widget and is styled with a white background.


  openInboxModal() {
    showModalBottomSheet(
      isScrollControlled: true,
      isDismissible: true,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      constraints: const BoxConstraints(
        maxWidth: double.infinity,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25.0), topRight: Radius.circular(25.0)),
      ),
      backgroundColor: Colors.white,
      context: context,
      builder: (bc) {
        return const InboxModal();
      },
    );
  }

  courierNotificationLister(int unreadMessageCount) {
    if (unreadMessageCount > 0) {
      if (_tabController!.index != 0) {
        isBottomNotificationEnable = true;
      } else {
        isBottomNotificationEnable = false;
      }

      notificationCount = unreadMessageCount;
      setState(
        () {},
      );
    }
  }

  showAlertPushClick() {
    showAPIDialog(context, "PushClicked");
  }
}
