// ignore_for_file: prefer_typing_uninitialized_variables

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart'; // Import for LogEvent enum
import 'package:legacylock_app/helper/consts/colors.dart';
import 'package:legacylock_app/helper/util/app_button.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:mime/mime.dart';

class AddPicturePage extends StatefulWidget {
  final parentClass;
  final String userName;
  final int userId;
  final bool isfromSignUp;
  const AddPicturePage({
    Key? key,
    required this.parentClass,
    required this.userName,
    required this.userId,
    required this.isfromSignUp,
  }) : super(key: key);

  @override
  State<AddPicturePage> createState() => _AddPicturePageState();
}

class _AddPicturePageState extends State<AddPicturePage> {
  XFile? _image;
  CroppedFile? croppedFile;
  String mimeType = "png";
  bool isSelected = false;

  @override
  void initState() {
    debugPrint("userId ${widget.userId}");
    debugPrint("userName ${widget.userName}");
    super.initState();
  }

  Future getImagePhone(isGallery) async {
    _image = await ImagePicker().pickImage(
        source: isGallery ? ImageSource.gallery : ImageSource.camera,
        maxHeight: 1024,
        maxWidth: 1024);
    getMimeType();
    _cropImage();
  }

  getMimeType() {
    mimeType = lookupMimeType(_image!.path)!;
  }

  Future<void> _cropImage() async {
    croppedFile = await ImageCropper().cropImage(
      sourcePath: _image!.path,
      compressFormat: ImageCompressFormat.jpg,
      compressQuality: 100,
      uiSettings: buildUiSettings(),
    );
    setState(() {
      if (croppedFile != null) {
        isSelected = true;
      }
    });
  }

  List<PlatformUiSettings>? buildUiSettings() {
    return [
      AndroidUiSettings(
        toolbarTitle: 'Cropper',
        toolbarColor: Colors.deepOrange,
        toolbarWidgetColor: Colors.white,
        initAspectRatio: CropAspectRatioPreset.original,
        lockAspectRatio: false,
        aspectRatioPresets: [
          CropAspectRatioPreset.square,
        ],
      ),
      IOSUiSettings(
        minimumAspectRatio: 1.0,
        aspectRatioLockEnabled: true, // Lock to square for circular crop
        rotateButtonsHidden: true,
        resetButtonHidden: true,
      ),
      /*AndroidUiSettings(
          toolbarTitle: 'Cropper',
          toolbarColor: Colors.deepOrange,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false),
      IOSUiSettings(
        title: 'Cropper',
      ),*/
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: kBgAuthColor,
        elevation: 0.0,
        foregroundColor: kButtonColor,
        title: createText(
          "Add a Profile Picture",
          20,
          textColor: kLightBlue,
          fontWeight: FontWeight.bold,
        ),
        centerTitle: true,
        automaticallyImplyLeading: false,
        leadingWidth: 60,
        leading: Padding(
          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
          child: RawMaterialButton(
            onPressed: () async {
              if (widget.isfromSignUp) {
                await loadMainPage();
              } else {
                Navigator.of(context).pop();
              }
            },
            elevation: 0,
            fillColor: kAppBarCross,
            shape: const CircleBorder(
              side: BorderSide(width: 0.5, color: kAppBarCrossBorder),
            ),
            child: const Icon(
              Icons.close,
              size: 30.0,
              color: kLightBlue,
            ),
          ),
        ),
      ),
      backgroundColor: kBgAuthColor,
      body: ListView(
        children: [
          const SizedBox(
            height: 20,
          ),
          Padding(
              padding: const EdgeInsets.only(left: 10, right: 10),
              child: Column(
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width - 40,
                    child: createText(
                      "${widget.userName}, let’s add a photo of you",
                      20.0,
                      textAlign: TextAlign.left,
                      fontWeight: FontWeight.w500,
                      textColor: kLightBlue,
                    ),
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  SizedBox(
                    width: MediaQuery.of(context).size.width - 40,
                    child: createText(
                      "This will make it easier for your family and friends to recognize you.",
                      16.0,
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              )),
          const SizedBox(
            height: 50,
          ),
          Center(
            child: SizedBox(
              height: 200,
              width: 200,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(170),
                child: croppedFile != null
                    ? Image(
                        height: 200,
                        width: 200,
                        image: FileImage(
                          File(croppedFile!.path),
                        ),
                        fit: BoxFit.cover,
                      )
                    : const CircleAvatar(
                        radius: 100,
                        backgroundColor: Colors.grey,
                        child: Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 150,
                        ),
                      ),
              ),
            ),
          ),
          const SizedBox(
            height: 100,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 40, right: 40),
            child: SizedBox(
              height: 50,
              child: TextButton.icon(
                onPressed: () {
                  getImagePhone(false);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromARGB(255, 232, 233, 250),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                ),
                icon: const Icon(
                  Icons.camera_alt,
                  color: Colors.black,
                  size: 25,
                ),
                label: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: createText("Take a photo", 16),
                ),
              ),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 40, right: 40),
            child: SizedBox(
              height: 50,
              child: TextButton.icon(
                onPressed: () {
                  getImagePhone(true);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromARGB(255, 230, 235, 250),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                ),
                icon: const Icon(
                  Icons.photo_size_select_actual,
                  color: Colors.black54,
                  size: 25,
                ),
                label: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: createText("Select from camera roll", 16),
                ),
              ),
            ),
          ),
          SizedBox(
            height: isSelected ? 40 : 10,
          ),
          isSelected
              ? ButtonView(
                  buttonText: "Looks good!",
                  bgColor: kLightBlue,
                  onClicked: () async {
                    await VideoTC().accountBloc!.uploadProfilePic(
                        croppedFile!,
                        widget.parentClass,
                        context,
                        widget.userId,
                        widget.isfromSignUp,
                        mimeType);
                  },
                )
              : Container(
                  width: 100,
                  height: 50,
                  alignment: Alignment.center,
                  child: TextButton(
                    onPressed: () async {
                      // Track skipping profile picture
                      if (widget.isfromSignUp) {
                        debugPrint("Tracking profile picture screen: skipped");
                        await VideoTC().setLogEvent(LogEvent.passProfilePicScreen, isUploaded: false);
                      }
                      
                      if (widget.isfromSignUp) {
                        await loadMainPage();
                      } else {
                        Navigator.of(context).pop();
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kTransparent,
                    ),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: createText("Skip for now", 16),
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  showCameraPopUp() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: kTransparent,
      builder: (context) {
        return Container(
            height: 300,
            padding: const EdgeInsets.all(32),
            decoration: const BoxDecoration(
              color: kPageBackgroundColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25.0),
                topRight: Radius.circular(25.0),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  "Select your profile image from: ",
                  textAlign: TextAlign.left,
                  style: TextStyle(
                      fontSize: 16, fontWeight: FontWeight.w700, color: kBlack),
                ),
                const SizedBox(
                  height: 30,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    getImagePhone(true);
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 100),
                    width: 250,
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        border: Border.all(
                          color: kLightGreen,
                        ),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(20))),
                    child: const Row(
                      children: [
                        Text(
                          "1. Gallery",
                          textAlign: TextAlign.left,
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: kBlack),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Icon(
                          Icons.image,
                          color: kButtonColor,
                        )
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    getImagePhone(false);
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 100),
                    width: 250,
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        border: Border.all(
                          color: kLightGreen,
                        ),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(20))),
                    child: const Row(
                      children: [
                        Text(
                          "2. Camera",
                          textAlign: TextAlign.left,
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: kBlack),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Icon(
                          Icons.camera_alt,
                          color: kButtonColor,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ));
      },
    );
  }

  loadMainPage() async {
    await VideoTC().loadMainRoute(context, isLoading: true);
  }
}
