import 'package:country_pickers/country.dart';
import 'package:country_pickers/utils/utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:legacylock_app/data/api/register_bloc.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart';
import 'package:legacylock_app/helper/consts/colors.dart';
import 'package:legacylock_app/helper/util/app_button.dart';
import 'package:legacylock_app/helper/util/custom_phone_field.dart';
import 'package:legacylock_app/helper/util/edit_text_util.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:legacylock_app/router/route_constants.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

enum SubmitButtonState { off, grey, ok, submit }

class _LoginPageState extends State<LoginPage> {
  String phoneCode = "+1";
  String countryCode = "US";
  Country? country;
  bool isNumberOk = false;
  SubmitButtonState submitButtonState = SubmitButtonState.off;
  bool isLoadingEnable = false;
  bool isBackButtonEnable = false;

  var phoneController = TextEditingController();
  var passwordController = TextEditingController();
  final GlobalKey<FormFieldState> phoneFormFieldKey = GlobalKey();
  final GlobalKey<FormFieldState> passwordFormFieldKey = GlobalKey();
  FocusNode phoneNumberFocusNode = FocusNode();
  FocusNode passwardFocusNode = FocusNode();

  ScrollController scrollController = ScrollController();

  @override
  void initState() {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      systemNavigationBarColor: kBgAuthColor,
      statusBarColor: kBgAuthColor,
      statusBarIconBrightness: Brightness.dark,
    ));
    country = CountryPickerUtils.getCountryByIsoCode("US");
    if (kDebugMode) {
      phoneCode = "+880";
      countryCode = "BD";
      country = CountryPickerUtils.getCountryByIsoCode("BD");
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kBgAuthColor,
      appBar: AppBar(
        backgroundColor: kBgAuthColor,
        leading: Padding(
          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
          child: RawMaterialButton(
            onPressed: () {
              if (isBackButtonEnable == true) {
                setState(() {
                  isNumberOk = false;
                  isBackButtonEnable = false;
                  submitButtonState = SubmitButtonState.ok;
                });
              } else {
                Navigator.of(context).pushNamedAndRemoveUntil(
                    welcomeRoute, (Route<dynamic> route) => false);
              }
            },
            elevation: 0.0,
            fillColor: kAppBarCross,
            shape: const CircleBorder(
              side: BorderSide(width: 1.0, color: kAppBarCrossBorder),
            ),
            child: const Icon(
              Icons.arrow_back,
              size: 20.0,
              color: kLightBlue,
            ),
          ),
        ),
      ),
      body: ListView(
        shrinkWrap: true,
        controller: scrollController,
        children: [
          const SizedBox(
            height: 60,
          ),
          const Image(
            width: 237,
            height: 140,
            image: AssetImage("assets/images/cs_logo.png"),
          ),
          const SizedBox(
            height: 50,
          ),
          Column(
            children: [
              /*SizedBox(
                width: MediaQuery.of(context).size.width,
                height: 30,
                child: Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: createText(
                    "Phone Number",
                    16.0,
                    textAlign: TextAlign.left,
                    textColor: kPasswordText,
                    fontType: FontType.cabin,
                  ),
                ),
              ),*/
              PhoneEditTextView(
                  // label: "Phone Number",
                  textInputAction: TextInputAction.done,
                  keyboardType: TextInputType.phone,
                  hintText: "Your phone number",
                  hintMaxLines: null,
                  //    initialFlag: "US",
                  country: country,
                  focusNode: phoneNumberFocusNode,
                  autofocus: true,
                  formFieldKey: phoneFormFieldKey,
                  controller: phoneController,
                  fontType: FontType.cabin,
                  isLoadingEnable: isLoadingEnable,
                  enabled: submitButtonState == SubmitButtonState.submit
                      ? false
                      : true,
                  validateCallBack: (phone) async => {
                        await checkValidate(phone),
                      },
                  getSelectedCode: (Country coun) {
                    setState(() {
                      country = coun;
                      /*debugPrint("country isoCode ${country!.isoCode}");
                      debugPrint("country iso3Code ${country!.iso3Code}");
                      debugPrint("country phoneCode ${country!.phoneCode}");
                      debugPrint("country name ${country!.name}");*/
                      phoneCode = "+${country!.phoneCode}";
                      countryCode = country!.isoCode;
                      debugPrint("countryCode $countryCode");

                      Future.delayed(Duration.zero, () async {
                        await checkValidate(phoneController.text);
                      });
                    });
                  },
                  validate: (phone) {
                    return !validatePhone(phoneCode + phone!)
                        ? "Phone number is invalid."
                        : null;
                  }),
              const SizedBox(
                height: 30,
              ),
              /*SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: createText(
                    "Password",
                    16.0,
                    textAlign: TextAlign.left,
                    textColor: kPasswordText,
                    fontType: FontType.cabin,
                  ),
                ),
              ),*/
              isNumberOk == true
                  ? Container(
                      margin: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      padding: EdgeInsets.zero,
                      height: 56,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: const BorderRadius.all(Radius.circular(50))),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              key: passwordFormFieldKey,
                              validator: (password) {
                                return (password!.isEmpty || password.length < 8)
                                    ? "Password must be at least 8 characters."
                                    : null;
                              },
                              controller: passwordController,
                              textInputAction: TextInputAction.done,
                              keyboardType: TextInputType.visiblePassword,
                              obscureText: true,
                              autofocus: true,
                              focusNode: passwardFocusNode,
                              style: TextStyle(
                                color: kMediumGreyColor,
                                fontSize: fontSizeForLargeText(context, 14),
                                fontFamily: getFontFamily(fontType: FontType.cabin),
                              ),
                              decoration: InputDecoration(
                                isDense: true,
                                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                border: InputBorder.none,
                                hintText: "Password",
                                hintStyle: TextStyle(
                                  fontSize: fontSizeForLargeText(context, 14),
                                  fontFamily: getFontFamily(fontType: FontType.cabin),
                                  color: kGreyColor,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  : const SizedBox(),
              isNumberOk == true
                  ? Container(
                      padding: const EdgeInsets.only(right: 16),
                      alignment: Alignment.topRight,
                      child: TextButton(
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          foregroundColor: kButtonColor,
                          splashFactory: NoSplash.splashFactory,
                          // overlayColor: MaterialStateProperty.all(Colors.transparent),
                          textStyle: TextStyle(
                            fontFamily: getFontFamily(),
                            fontWeight: FontWeight.w400,
                            fontSize: fontSizeForLargeText(context, 16),
                            color: kLightGreen,
                          ),
                        ),
                        onPressed: () {
                          Navigator.of(context)
                              .pushNamed(forgotRoute, arguments: {
                            "parentClass": this,
                            "stepCount": 1,
                            "country": country,
                            "phoneNumber": phoneController.text.trim(),
                          });
                        },
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: createText(
                            "Forgot password?",
                            fontSizeForLargeText(context, 16),
                            textColor: kLightBlue,
                            fontType: FontType.cabin,
                          ),
                        ),
                      ),
                    )
                  : const SizedBox(),
              submitButtonState != SubmitButtonState.off &&
                      submitButtonState != SubmitButtonState.submit
                  ? ButtonView(
                      buttonText: "Submit",
                      borderRadius: 30,
                      fontSize: 20,
                      buttonTextColor: submitButtonState == SubmitButtonState.ok
                          ? kWhiteColor
                          : kDetativateText,
                      bgColor: submitButtonState == SubmitButtonState.ok
                          ? kLightBlue
                          : const Color.fromARGB(255, 225, 225, 225),
                      onClicked: () async {
                        if (submitButtonState == SubmitButtonState.ok) {
                          setState(() {
                            submitButtonState = SubmitButtonState.submit;
                            phoneNumberFocusNode.unfocus();
                            isLoadingEnable = true;
                          });

                          await VideoTC().accountBloc!.checkUserNumber(
                              this, phoneCode + phoneController.text.trim());
                          // //loadUserExistsInfo(true);
                          // loadUserExistsInfo(false);
                        }
                      },
                    )
                  : const SizedBox(),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          isNumberOk == true
              ? Column(
                  children: [
                    ButtonView(
                        buttonText: "Log in",
                        fontSize: 20,
                        borderRadius: 30,
                        bgColor: kLightBlue,
                        onClicked: () {
                          validateField();
                        }),
                    /* const SizedBox(
                height: 16,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  createText(
                    "Don't have an account? ",
                    16,
                    // fontWeight: FontWeight.w400,
                    textColor: kMediumGreyColor,
                    fontType: FontType.cabin,
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.pushNamed(context, registerRoute);
                      // Navigator.of(context).pushNamedAndRemoveUntil(
                      //     registerRoute, (Route<dynamic> route) => false);
                    },
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: 'Sign Up',
                            style: TextStyle(
                              fontFamily:
                                  getFontFamily(fontType: FontType.cabin),
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                              color: kLightBlue,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),*/
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                )
              : const SizedBox(),
        ],
      ),
    );
  }

  /// Validate the phone number and password fields, and if they are valid, then
  /// authenticate the user.
  validateField() {
    var userNameValidate = phoneFormFieldKey.currentState!.validate();
    var passwordValidate = passwordFormFieldKey.currentState!.validate();
    if (!userNameValidate || !passwordValidate) {
    } else {
      // Utility.instance.hideKeyboard(context);
      VideoTC().accountBloc!.authenticateUser(
            this,
            phoneCode + phoneController.text.trim(),
            passwordController.text.trim(),
          );
    }
  }

  passwordWrong() {
    setState(() {
      submitButtonState = SubmitButtonState.ok;
      isLoadingEnable = false;
      isNumberOk = false;
      passwordController.text = "";
    });
  }

  @override
  void dispose() {
    // _loginBloc!.dispose();
    super.dispose();
  }

  reloadPage() {
    setState(() {
      submitButtonState = SubmitButtonState.ok;
      isNumberOk = false;
    });
  }

  checkValidate(phone) async {
    if (phone!.toString().isEmpty) {
      setState(() {
        submitButtonState = SubmitButtonState.off;
      });

      return;
    } else if (phone!.toString().length == 1) {
      setState(() {
        submitButtonState = SubmitButtonState.grey;
      });
      return;
    }
    try {
      // Map<String, dynamic> result =
      await parse(
        phoneCode + phone!,
        region: countryCode,
      );

      setState(() {
        submitButtonState = SubmitButtonState.ok;
      });
    } catch (e) {
      setState(() {
        submitButtonState = SubmitButtonState.grey;
      });
    }
  }

  loadUserExistsInfo(bool isUserExists) {
    if (isUserExists == true) {
      setState(() {
        isNumberOk = true;
        isLoadingEnable = false;
        isBackButtonEnable = true;
        Future.delayed(const Duration(seconds: 1), () {
          setLoginButton(true);
        });
      });
      // Track phone number entry event with hasAccount=true
      VideoTC().setLogEvent(LogEvent.enterPhoneNumber, hasAccount: true);
    } else {
      setState(() {
        isLoadingEnable = false;
      });
      // Track phone number entry event with hasAccount=false
      VideoTC().setLogEvent(LogEvent.enterPhoneNumber, hasAccount: false);
      Future.delayed(const Duration(milliseconds: 100), () {
        if (!mounted) return;
        RegisterBloc registerBloc = RegisterBloc();
        registerBloc.country = country!;
        registerBloc.phoneCode = phoneCode;
        registerBloc.phoneController.text = phoneController.text.trim();
        Navigator.of(context).pushNamed(verifyPinRoute, arguments: {
          "parentClass": this,
          "registerBloc": registerBloc,
        });
      });
    }
  }

  setLoginButton(bool isShow) {
    if (isShow) {
      scrollController.animateTo(
        scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 100),
        curve: Curves.easeInOut,
      );
    }
  }
}
