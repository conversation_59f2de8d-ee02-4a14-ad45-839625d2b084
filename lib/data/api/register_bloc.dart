import 'dart:async';
import 'dart:convert';
import 'package:country_pickers/country.dart';
import 'package:country_pickers/utils/utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart';
import 'package:legacylock_app/data/model/invitation.dart';
import 'package:legacylock_app/data/model/user_profile.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart';
import 'package:legacylock_app/helper/consts/urls.dart';
import 'package:legacylock_app/helper/network/api_response.dart';
import 'package:legacylock_app/helper/util/utility_class.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:legacylock_app/router/route_constants.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class RegisterBloc {
  String phoneCode = "+1";
  String countryCode = "US";
  Country country = CountryPickerUtils.getCountryByIsoCode("US");

  var firstNameController = TextEditingController();
  var middleNameController = TextEditingController();
  var lastNameController = TextEditingController();
  var phoneController = TextEditingController();
  var passwordController = TextEditingController();
  var confirmPasswordController = TextEditingController();

  final GlobalKey<FormFieldState> firstNameFormFieldKey = GlobalKey();
  final GlobalKey<FormFieldState> middleNameFormFieldKey = GlobalKey();
  final GlobalKey<FormFieldState> lastNameFormFieldKey = GlobalKey();
  final GlobalKey<FormFieldState> phoneFormFieldKey = GlobalKey();
  final GlobalKey<FormFieldState> passwordFormFieldKey = GlobalKey();
  final GlobalKey<FormFieldState> confirmPasswordFormFieldKey = GlobalKey();

  int registerUserid = 0;

  StreamController<ErrorAnimationType>? errorController;
  List<UserProfile> allInvitationProfile = [];

  doUserRegistration(context) async {
    registerUserid = 0;
    String registerApiUrl = "${baseApiUrl}register";
    debugPrint("registerApiUrl $registerApiUrl");

    if (await Utility.instance.checkConnection()) {
      innerAuthLoader(context);
      var body = json.encode({
        // "first_name": firstNameController.text.trim(),
        // "middle_name": middleNameController.text.trim(),
        // "last_name": lastNameController.text.trim(),
        // "password": passwordController.text.trim(),
        "phone_number": phoneCode + phoneController.text.trim(),
      });

      debugPrint("body $body");

      Response? response = await ApiUrlResponse.apiRequestContext(
          context, HttpRequestType.post, registerApiUrl,
          body: body, isAccessToken: false);

      debugPrint("doUserRegistration response.body ${response!.body}");
      debugPrint("response ${response!.statusCode}");

      if (response!.statusCode != 302) {
        Map<String, dynamic> jsonData = json.decode(response.body);

        registerUserid =
            jsonData["data"].containsKey("id") ? jsonData["data"]['id'] : 0;
        debugPrint("registerUserid $registerUserid");
        debugPrint("User not Exits!");

        // registerPageState.setStepCount(1);

        Navigator.of(context).pop();
        // Navigator.of(context).pushNamed(verifyPinRoute, arguments: {
        //   "registerBloc": this,
        // });
      } else {
        // String message = json.decode(response.body).containsKey("message")
        //     ? json.decode(response.body)['message']
        //     : "";
        // widget.parentClass.setStepCount(-1);
        Navigator.of(context).pop();
        showGoLoginRouteDialog(context, "User Exits!");
      }
    } else {
      showAPIDialog(context, "No internet connection");
    }
  }

  verifySignUpPin(parentClass, code) async {
    var verifyPinUrl = "${baseApiUrl}verify";
    debugPrint("verifySignUpPin $verifyPinUrl");

    String body = json.encode({
      "phone_number": phoneCode + phoneController.text,
      "code": code,
      // "password": passwordController.text.trim(),
    });

    Response? response = await ApiUrlResponse.apiRequestContext(
        parentClass.context, HttpRequestType.post, verifyPinUrl,
        body: body, isLoading: false);
    if (response != null) {
      debugPrint("verifySignUpPin response.body ${response.body}");

      if (response.statusCode == 200) {
        Map<String, dynamic> jsonData = json.decode(response.body);
        // debugPrint("registerUserid-----R $registerUserid");

        VideoTC().saveAccesToken(jsonData["AccessToken"]);
        VideoTC().saveUserId(DefaultValue.kDefaultInt,
            DefaultValue.kDefaultString, DefaultValue.kDefaultString);
        await getInvitationProfileList(parentClass);
        // debugPrint("registerUserid $registerUserid");
        /*UserProfile? currentUserProfile =
            await VideoTC().accountBloc!.getUserProfile(registerUserid);
        if (currentUserProfile != null) {
          debugPrint(
              "currentUserProfile ${currentUserProfile.fromUserRelationshipsDetails!.length}");
        }*/


        // Track successful phone verification
        // Assuming this is the first attempt, set numAttempts to 1
        // You may need to track actual attempts if you have that information
        debugPrint("Tracking phone verification event with numAttempts: 1");
        await VideoTC().setLogEvent(LogEvent.verifyPhoneNumber, numAttempts: 1);

        parentClass.varificationRespose(true);
        /*if (registerUserid == 0) {
          String message = json.decode(response.body).containsKey("message")
              ? json.decode(response.body)['message']
              : "";
          
           showGoLoginRouteDialog(context, message);
        } else {
        *Navigator.of(context).pushNamedAndRemoveUntil(
              addPictureRoute,
              arguments: {
                "userName": firstNameController.text.trim(),
                "userId": registerUserid,
                "isfromSignUp": true,
              },
              (Route<dynamic> route) => false);
        }*/
      } else {
        parentClass.varificationRespose(false);
        // Navigator.of(parentClass.context).pop();
        // showAPIDialog(parentClass.context, "Verification Failed");
      }
    } else {
      parentClass.varificationRespose(false);
      // Navigator.of(parentClass.context).pop();
      // showAPIDialog(parentClass.context, "Verification Failed");
    }
  }

  validatePhoneField(context, checkedValue, registerPageState) async {
    var firstNameValidate = firstNameFormFieldKey.currentState!.validate();
    var lastNameValidate = lastNameFormFieldKey.currentState!.validate();
    // var phoneValidate = phoneFormFieldKey.currentState!.validate();|| !phoneValidate
    if (!firstNameValidate || !lastNameValidate) {
    } else if (!checkedValue) {
      showAPIDialog(context, "Please agree with our terms and conditions");
    } else {
      Utility.instance.hideKeyboard(context);
      
      // Track the event when user enters name and accepts terms
      debugPrint("Tracking name entry and terms acceptance");
      await VideoTC().setLogEvent(LogEvent.enterNameAndAcceptTerms, numAttempts: 1);
      
      registerPageState.setStepCount(2);
      /*var checkPhoneNumberUrl = "${baseApiUrl}find-by-phone-number";
      debugPrint("checkPhneNumber $checkPhoneNumberUrl");

      var body = json.encode({
        "phone_number": phoneCode + phoneController.text.trim(),
      });

      Response? response = await ApiUrlResponse.apiRequestContext(
          context, HttpRequestType.post, checkPhoneNumberUrl,
          body: body, isAccessToken: false);
      if (response != null) {
        debugPrint("response.body----- ${response.body}");
        debugPrint("response.statusCode ${response.statusCode}");
        if (response.statusCode == 200) {
          String message = json.decode(response.body).containsKey("message")
              ? json.decode(response.body)['message']
              : "";
          debugPrint("message $message");
          showGoLoginRouteDialog(context, "User already Exits!");
        } else if (response.statusCode == 204) {
          registerPageState.setStepCount(2);
        } else {
          Navigator.of(context).pop();
          registerPageState.setStepCount(2);
        }
      } else {
        Navigator.of(context).pop();
        showAPIDialog(context, "There is a connection problem");
      }*/
    }
  }

  validPasswordField(context, registerPageState) {
    var passwordValidate = passwordFormFieldKey.currentState!.validate();
    var confirmPasswordValidate =
        confirmPasswordFormFieldKey.currentState!.validate();
    if (!passwordValidate || !confirmPasswordValidate) {
    } else {
      // doUserRegistration(context);
      finalizeUserRegistration(context);
    }
  }

  void finalizeUserRegistration(context) async {
    String userRegistrationUrl = "${baseApiUrl}user/$registerUserid";
    debugPrint("finalizeUserRegistration $userRegistrationUrl");
    var body = json.encode({
      "first_name": firstNameController.text.trim(),
      "middle_name": middleNameController.text.trim(),
      "last_name": lastNameController.text.trim(),
      "password": passwordController.text.trim(),
      "phone_number": phoneCode + phoneController.text.trim(),
    });

    debugPrint("body $body");
    innerAuthLoader(context);
    Response? response = await ApiUrlResponse.apiRequestContext(
        context, HttpRequestType.put, userRegistrationUrl,
        body: body, isLoading: false);

    if (response != null) {
      debugPrint("doUserRegistration response.body ${response.body}");
      debugPrint("response ${response.statusCode}");

      if (response.statusCode == 200) {
        // Map<String, dynamic> jsonData = json.decode(response.body);

        // Track password creation event
        debugPrint("Tracking password creation event");
        await VideoTC().setLogEvent(LogEvent.createPassword, numAttempts: 1);
        
        await VideoTC().setCurrentUserFromUserId(
            registerUserid,
            (phoneCode + phoneController.text.trim()).toString(),
            passwordController.text.trim());
        await VideoTC().setLogEvent(LogEvent.register);
        Utility.instance.hideKeyboard(context);
        Navigator.of(context).pop();
        Navigator.of(context).pop();
        Navigator.of(context).pushNamed(
          addPictureRoute,
          arguments: {
            "userName": firstNameController.text.trim(),
            "userId": registerUserid,
            "isfromSignUp": true,
          },
        );
      } else {
        Navigator.of(context).pop();
        showAPIDialog(context, "User sineUp Failed.");
      }
    } else {
      Navigator.of(context).pop();
      showAPIDialog(context, "User sineUp Failed.");
    }
  }

  showGoLoginRouteDialog(context, message) {
    showCupertinoDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return CupertinoAlertDialog(
          content: Text(
            message,
            style: TextStyle(
                // color: kBlack,
                fontFamily: getFontFamily(),
                fontWeight: FontWeight.w500,
                fontSize: 16),
          ),
          actions: [
            CupertinoDialogAction(
                child: createText("OK", 15, fontWeight: FontWeight.w500),
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushNamedAndRemoveUntil(
                      logInRoute, (Route<dynamic> route) => false);
                }),
          ],
        );
      },
    );
  }

  resendVerificationPin(context) async {
    var resendVerificationPinUrl = "${baseApiUrl}resend-verification-code";

    debugPrint("resendVerificationPinUrl $resendVerificationPinUrl");
    var body = json.encode({
      "phone_number": phoneController.text,
    });
    debugPrint("body $body");

    Response? response = await ApiUrlResponse.apiRequestContext(
        context, HttpRequestType.post, resendVerificationPinUrl,
        body: body);
    if (response != null) {
      //Navigator.of(context).pop();
    } else {
      //Navigator.of(context).pop();
    }

    /*if (await Utility.instance.checkConnection()) {
      //  String phone = await SharedPrefUtil.getString(kPrefUserPhone);
      
      var body = json.encode({
        "username": username,
        //  "phone": phone
      });
      try {
        var response = await http.post(Uri.parse(url), body: body);
        showAPIDialog(context,
            "A verification code has been sent to you by text message");
      } catch (e, stack) {
        bugsnag.notify(e, stack);
        showAPIDialog(context, "Something went wrong. Try again");
      }
    } else {
      showAPIDialog(context, "No internet connection");
    }*/
  }

  getInvitationProfileList(parentClass) async {
    allInvitationProfile = [];
    String invitationUrl =
        "${baseApiUrl}invitation?to_phone_number=${phoneCode + phoneController.text.trim()}";

    debugPrint("invitationUrl $invitationUrl");

    Response? response = await ApiUrlResponse.apiRequestContext(
      parentClass.context,
      HttpRequestType.get,
      invitationUrl,
      isLoading: false,
    );

    if (response != null) {
      debugPrint("response.body ${response.body}");
      debugPrint("response ${response.statusCode}");

      if (response.statusCode == 200) {
        Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint("jsonData ${jsonData["data"]}");
        List<dynamic> allData = jsonData["data"];
        for (int i = 0; i < allData.length; i++) {
          Invitation invitation = Invitation.fromMap(allData[i]);
          if (invitation.fromUser != null) {
            allInvitationProfile.add(invitation.fromUser!);
          }
        }
        debugPrint("allInvitationProfile ${allInvitationProfile.length}");
      } else {
        showAPIDialog(parentClass.context, "Connection Error.");
      }
    } else {
      showAPIDialog(parentClass.context, "Connection Error.");
    }
  }

  void dispose() {
    firstNameController.dispose();
    middleNameController.dispose();
    lastNameController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    phoneController.dispose();
  }
}
