// ignore_for_file: non_constant_identifier_names, prefer_typing_uninitialized_variables

import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:http/http.dart';
import 'package:intl/intl.dart';
import 'package:legacylock_app/data/api/user_profile_bloc.dart';
import 'package:legacylock_app/data/model/question_activity.dart';
import 'package:legacylock_app/data/model/question_sends.dart';
import 'package:legacylock_app/data/model/questions.dart';
import 'package:legacylock_app/data/model/tags.dart';
import 'package:legacylock_app/data/model/user_profile.dart';
import 'package:legacylock_app/data/model/user_relationship.dart';
import 'package:legacylock_app/data/model/video.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart';
import 'package:legacylock_app/helper/consts/urls.dart';
import 'package:legacylock_app/helper/network/api_response.dart';
import 'package:legacylock_app/helper/util/view_util.dart';

LoadingStatus allQuestionListStatus = LoadingStatus.notStarted;

class QuestionBloc {
  TextEditingController customQuestionController = TextEditingController();
  LoadingStatus questionListStatus = LoadingStatus.notStarted;
  LoadingStatus inboxOutboxCountStatus = LoadingStatus.notStarted;
  LoadingStatus tagListStatus = LoadingStatus.notStarted;
  LoadingStatus questionInboxListStatus = LoadingStatus.notStarted;
  LoadingStatus questionOutboxListStatus = LoadingStatus.notStarted;
  LoadingStatus questionSendListStatus = LoadingStatus.notStarted;
  bool isInboxUserDataLoad = false;

  int? videoSentUserId;
  int? questionSentId;
  int? questionId;
  QuestionSends? questionSendsToAnswser;
  QuestionSends? questionSendsToMySelf;

  int inboxCount = 0;
  int outboxCount = 0;

  validateCustomQuestionField(context) {}

  Future<Questions?> createCustomQuestion(context, Tags selectedTag,
      {bool isLoading = true}) async {
    var questionUrl = "${baseApiUrl}question";

    var body;
    if (selectedTag.id == 0) {
      body = json.encode({
        "question_text": customQuestionController.text,
        "creator__user_id": VideoTC().currentUserProfile!.id,
      });
    } else {
      body = json.encode({
        "question_text": customQuestionController.text,
        "creator__user_id": VideoTC().currentUserProfile!.id,
        "tag_id": selectedTag.id,
      });
    }
    debugPrint("body $body");

    Response? response = await ApiUrlResponse.apiRequestContext(
        context, HttpRequestType.post, questionUrl,
        body: body, isLoading: isLoading);
    if (response != null) {
      if (response.statusCode == 201) {
        Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint("jsonData $jsonData");
        Questions questions = Questions.fromMap(jsonData["data"]);
        questions.createdBy = VideoTC().currentUserProfile;
        questions.tags!.add(selectedTag);
        // questions.show();

        createCustomQuestionActivity(
            questions,
            QustionActivityStatus.custom.value,
            questions.createdBy,
            questions.createdBy,
            isCustomQuestion: true);
        debugPrint(
            "VideoTC().allQuestionList----------- ${VideoTC().allQuestionList.length}");
        VideoTC().allQuestionList.insert(0, questions);
        debugPrint(
            "VideoTC().allQuestionList ${VideoTC().allQuestionList.length}");
        return questions;
      } else {
        Navigator.of(context).pop();
        showAPIDialog(context, "Question Create Failed");
      }
    } else {
      Navigator.of(context).pop();
      showAPIDialog(context, "Question Create Failed");
    }
    return null;
  }

  /// Fetches all question tags from the API and updates the VideoTC instance with the tags.
  ///
  /// This method checks if the `allTags` list in the `VideoTC` instance is empty. 
  /// If it is, it makes a GET request to the tag API endpoint to retrieve the tags.
  /// The tags with the title "custom" are ignored. The tag titled "Career & Professional Journey"
  /// is repositioned to follow the tag titled "College / Early Adulthood" if present.
  /// Updates the `tagListStatus` to indicate the loading state or any errors encountered.
  /// If the `allTags` list is already populated, the status is set to complete without making a request.
  getAllQuestionsTag() async {
    VideoTC videoTC = VideoTC();
    if (videoTC.allTags.isEmpty) {
      tagListStatus = LoadingStatus.loading;
      var tagUrl = "${baseApiUrl}tag";
      Response? response =
          await ApiUrlResponse.apiRequest(HttpRequestType.get, tagUrl);
      if (response!.statusCode == 200) {
        Map<String, dynamic> jsonData = json.decode(response.body);
        List<dynamic> allData = jsonData["data"];
        for (int i = 0; i < allData.length; i++) {
          Tags tags = Tags.fromMap(allData[i]);
          //tags.title!.toLowerCase() != "followup"
          if (tags.title!.toLowerCase() != "custom") {
            videoTC.allTags.add(tags);
          }
        }
        Tags tagsToInsert = videoTC.allTags[videoTC.allTags.length - 1];
        if (tagsToInsert.title == "Career & Professional Journey") {
          videoTC.allTags.removeLast();
          int insertToTagIndex = -1;
          for (int i = 0; i < videoTC.allTags.length; i++) {
            Tags tag = videoTC.allTags[i];
            if (tag.title == "College / Early Adulthood") {
              insertToTagIndex = i + 1;
              break;
            }
          }
          if (insertToTagIndex != -1) {
            videoTC.allTags.insert(insertToTagIndex, tagsToInsert);
          }
        }
        tagListStatus = LoadingStatus.complete;
      } else {
        tagListStatus = LoadingStatus.error;
      }
    } else {
      tagListStatus = LoadingStatus.complete;
    }
  }

  /*getAllQuestionTag(parentClass) async {
    VideoTC videoTC = VideoTC();
    if (videoTC.allTags.isEmpty) {
      tagListStatus = LoadingStatus.loading;
      var questionUrl = "${baseApiUrl}tag";
      Response? response = await ApiUrlResponse.apiRequestContext(
          parentClass.context, HttpRequestType.get, questionUrl,
          isLoading: false);
      if (response!.statusCode == 200) {
        Map<String, dynamic> jsonData = json.decode(response.body);
        List<dynamic> allData = jsonData["data"];
        for (int i = 0; i < allData.length; i++) {
          Tags tags = Tags.fromMap(allData[i]);
          if (tags.title!.toLowerCase() != "followup") {
            videoTC.allTags.add(tags);
          }
        }
        parentClass.setState(() {
          tagListStatus = LoadingStatus.complete;
          // parentClass.allTags = VideoTC().allTags;
        });
      } else {
        parentClass.setState(() {
          tagListStatus = LoadingStatus.error;
        });
      }
    } else {
      // parentClass.setState(() {
      //   tagListStatus = LoadingStatus.complete;
      //   parentClass.allTags = videoTC.allTags;
      // });
    }
  }*/

  createCustomQuestionActivity(
    Questions questions,
    String questionStatus,
    UserProfile? sentFromUser,
    UserProfile? sentToUser, {
    bool isCustomQuestion = false,
    String? create_at = "",
    bool isInvite = false,
    String? invitation_name = "",
  }) {
    QuestionActivity questionActivity = QuestionActivity();
    questionActivity.id = 0;
    questionActivity.question_id = questions.id;
    questionActivity.question_status_id = 1;
    questionActivity.question_status = questionStatus;
    questionActivity.question_send_id = 0;
    questionActivity.sentFromUser = sentFromUser;
    questionActivity.sentToUser = sentToUser;
    questionActivity.created_at = questions.created_at;
    questionActivity.updated_at = questions.updated_at;

    if (questionActivity.question_status ==
        QustionActivityStatus.custom.value) {
      if (isCustomQuestion == true) {
        questions.questionActivities = [];
        DateTime dateTime = DateTime.parse(questionActivity.created_at!);
        questionActivity.date = dateTime.millisecondsSinceEpoch;
        questionActivity.activity_text =
            setQuestionActivityText(questionActivity);
        questions.questionActivities!.add(questionActivity);
      } else {
        questions.questionActivities!.insert(0, questionActivity);
      }
    } else if (questionActivity.question_status ==
        QustionActivityStatus.inbox.value) {
      questions.questionActivities = [];
      DateTime dateTime = DateTime.parse(questionActivity.created_at!);
      questionActivity.date = dateTime.millisecondsSinceEpoch;
      questionActivity.activity_text =
          setQuestionActivityText(questionActivity, isInbox: true);
      questions.questionActivities!.add(questionActivity);
    } else {
      if (create_at == "") {
        DateTime dateTime = DateTime.now();
        questionActivity.date = dateTime.millisecondsSinceEpoch;
      } else {
        DateTime dateTime = DateTime.parse(create_at!);
        questionActivity.date = dateTime.millisecondsSinceEpoch;
      }
      if (questionActivity.question_status ==
          QustionActivityStatus.sent.value) {
        questionActivity.activity_text = setQuestionActivityText(
            questionActivity,
            isInvite: true,
            invitation_name: invitation_name);
      } else {
        questionActivity.activity_text =
            setQuestionActivityText(questionActivity);
      }
      questions.questionActivities!.add(questionActivity);
    }
  }

  setQuestionActivityText(
    QuestionActivity questionActivity, {
    bool isInbox = false,
    bool isInvite = false,
    String? invitation_name = "",
  }) {
    String strSentFromUser = "You";

    if (questionActivity.sentFromUser != null) {
      strSentFromUser = questionActivity.sentFromUser!.first_name!;

      if (questionActivity.sentFromUser!.id ==
          VideoTC().currentUserProfile!.id) {
        strSentFromUser = "You";
      }
    }

    String strSentToUser = "you";
    if (questionActivity.sentToUser != null) {
      strSentToUser = questionActivity.sentToUser!.first_name!;
      if (questionActivity.sentToUser!.id == VideoTC().currentUserProfile!.id) {
        strSentToUser = "you";
      }
    } else {
      if (isInvite == true) {
        strSentToUser = invitation_name!;
      }
    }

    if (isInbox == true) {
      return "$strSentFromUser asked you..";
    }

    if (questionActivity.question_status == QustionActivityStatus.sent.value) {
      return "$strSentFromUser sent this question to $strSentToUser.";
    }
    if (questionActivity.question_status ==
        QustionActivityStatus.received.value) {
      return "$strSentToUser received this question.";
    }
    if (questionActivity.question_status ==
        QustionActivityStatus.answered.value) {
      if (questionActivity.sentToUser != null &&
          questionActivity.sentToUser!.id == VideoTC().currentUserProfile!.id) {
        return "You recorded a response.";
      }

      return "$strSentToUser answered this question.";
    }
    if (questionActivity.question_status ==
        QustionActivityStatus.resent.value) {
      return "$strSentFromUser resent this question.";
    }
    if (questionActivity.question_status ==
        QustionActivityStatus.custom.value) {
      return "$strSentFromUser created this question.";
    }

    return "activity";
  }

  getInboxOutboxCount(parentClass) async {
    if (inboxOutboxCountStatus != LoadingStatus.complete) {
      inboxOutboxCountStatus = LoadingStatus.loading;
      var inboxOutboxUrl = "${baseApiUrl}inbox-outbox-counts";
      debugPrint("inboxOutboxUrl $inboxOutboxUrl");

      Response? response = await ApiUrlResponse.apiRequestContext(
          parentClass.context, HttpRequestType.get, inboxOutboxUrl,
          isLoading: false);

      if (response!.statusCode == 200) {
        Map<String, dynamic> jsonData = json.decode(response.body);

        inboxCount =
            jsonData.containsKey("inbox_count") ? jsonData["inbox_count"] : 0;
        outboxCount =
            jsonData.containsKey("outbox_count") ? jsonData["outbox_count"] : 0;
        inboxOutboxCountStatus = LoadingStatus.complete;
        debugPrint("inboxCount $inboxCount");
      }
    } else {
      inboxOutboxCountStatus = LoadingStatus.complete;
    }
  }

  /// Loads all questions.
  ///
  /// If [allQuestionListStatus] is [LoadingStatus.notStarted], loads all questions
  /// from the server. If [allQuestionListStatus] is [LoadingStatus.loading], does
  /// nothing. If [allQuestionListStatus] is [LoadingStatus.complete], loads the
  /// questions from the cached list. If [allQuestionListStatus] is
  /// [LoadingStatus.error], does nothing.
  ///
  /// If the server returns a 200 status code, loads the questions and updates
  /// [allQuestionListStatus] to [LoadingStatus.complete]. If the server returns
  /// a non-200 status code, updates [allQuestionListStatus] to
  /// [LoadingStatus.error].
  ///
  /// If the cached list is not empty, loads the questions from the cached list.
  /// Otherwise, loads the questions from the server.
  ///
  /// If the user is not logged in, does nothing.
  ///
  /// If [isLoading] is true, shows the loading indicator. Otherwise, hides the
  /// loading indicator.
  ///
  /// Returns a [Future] that completes when the questions are loaded.

  getAllQuestionsList() async {
    if (allQuestionListStatus == LoadingStatus.notStarted) {
      VideoTC().allQuestionList.clear();
      questionListStatus = LoadingStatus.loading;
      allQuestionListStatus = LoadingStatus.loading;
      int pageIndex = 1;
      int maxQuestionSize = 0;
      int maxQuestionIndex = -1;
      while (true) {
        var questionUrl =
            "${baseApiUrl}question?include=question.createdBy,question.questionActivities,question.tags&page=$pageIndex&order_direction=asc";

        Response? response = await ApiUrlResponse.apiRequest(
          HttpRequestType.get,
          questionUrl,
        );

        if (response!.statusCode == 200) {
          Map<String, dynamic> jsonData = json.decode(response.body);
          List<dynamic> allData = jsonData["data"];

          debugPrint("jsonData $jsonData");
          debugPrint("jsonData ${allData.length}");

          for (int i = 0; i < allData.length; i++) {
            Questions questions = Questions.fromMap(allData[i]);
            if (maxQuestionIndex == -1 ||
                questions.question_text!.length > maxQuestionSize) {
              maxQuestionSize = questions.question_text!.length;
              maxQuestionIndex = i;
            }

            if (questions.createdBy != null) {
              createCustomQuestionActivity(
                questions,
                QustionActivityStatus.custom.value,
                questions.createdBy,
                questions.createdBy,
              );
            }

            for (int j = 0; j < questions.questionActivities!.length; j++) {
              QuestionActivity questionActivity =
                  questions.questionActivities![j];

              DateTime dateTime = DateTime.parse(questionActivity.created_at!);
              questionActivity.date = dateTime.millisecondsSinceEpoch;
              questionActivity.activity_text =
                  setQuestionActivityText(questionActivity);
            }
            VideoTC().allQuestionList.add(questions);
            // questions.show();
          }

          pageIndex++;

          if (allData.isEmpty) {
            allQuestionListStatus = LoadingStatus.complete;
            break;
          }
        } else {
          questionListStatus = LoadingStatus.error;
          break;
        }
      }
      // debugPrint("maxQuestionIndex $maxQuestionIndex");
      if (maxQuestionIndex != -1) {
        /*debugPrint(
            "VideoTC().allQuestionList ${VideoTC().allQuestionList.length}");
        Questions maxQuestions = VideoTC().allQuestionList[maxQuestionIndex];
        debugPrint(
            "questions.question_text!.length ${maxQuestions.question_text!.length}");
        debugPrint("questions.question_text ${maxQuestions.question_text}");*/
      }
      if (questionListStatus != LoadingStatus.error) {
        questionListStatus = LoadingStatus.complete;
      }
    }
  }

  getCustomQuestionsList(parentClass, selectedProfileId) async {
    var customQustionUrl =
        "${baseApiUrl}question?creator__user_id=$selectedProfileId";
    debugPrint("customQustionUrl $customQustionUrl");

    Response? response =
        await ApiUrlResponse.apiRequest(HttpRequestType.get, customQustionUrl);

    List<Questions> allQuestions = [];
    if (response!.statusCode == 200) {
      Map<String, dynamic> jsonData = json.decode(response.body);
      List<dynamic> allData = jsonData["data"];

      for (int i = 0; i < allData.length; i++) {
        Questions questions = Questions.fromMap(allData[i]);

        createCustomQuestionActivity(
            questions,
            QustionActivityStatus.custom.value,
            questions.createdBy,
            questions.createdBy,
            isCustomQuestion: true);
        // VideoTC().allQuestionList.insert(0, questions);
        allQuestions.add(questions);
      }
    }
    parentClass.addQuestionsInQuestionList(allQuestions);
  }

  /// Loads the questions that have been sent to the current user.
  ///
  /// Updates [questionInboxListStatus] to [LoadingStatus.loading] while the
  /// questions are being loaded.
  ///
  /// If the server returns a 200 status code, loads the questions and updates
  /// [questionInboxListStatus] to [LoadingStatus.complete]. If the server
  /// returns a non-200 status code, updates [questionInboxListStatus] to
  /// [LoadingStatus.error].
  ///
  /// If the user is not logged in, does nothing.
  ///
  /// If [isLoading] is true, shows the loading indicator. Otherwise, hides the
  /// loading indicator.
  ///
  /// Returns a [Future] that completes when the questions are loaded.
  getInboxQuestionsList() async {
    debugPrint("questionInboxListStatus $questionInboxListStatus");
    if (questionInboxListStatus == LoadingStatus.notStarted) {
      VideoTC().allInboxQuestionList.clear();
      questionInboxListStatus = LoadingStatus.loading;
      var questionInBoxUrl =
          "${baseApiUrl}question-send?sent_to_user_id=${VideoTC().currentUserProfile!.id}&include=video.question,question.tags,video.owner";
      debugPrint("questionInBoxUrl $questionInBoxUrl");

      Response? response = await ApiUrlResponse.apiRequest(
        HttpRequestType.get,
        questionInBoxUrl,
      );
      if (response!.statusCode == 200) {
        Map<String, dynamic> jsonData = json.decode(response.body);
        List<dynamic> allData = jsonData["data"];

        debugPrint("getInboxQuestionsList jsonData $jsonData");
        debugPrint("getInboxQuestionsList ${allData.length}");
        for (int i = 0; i < allData.length; i++) {
          QuestionSends questionSends = QuestionSends.fromMap(allData[i]);

          /*createCustomQuestionActivity(
              questionSends.question!,
              QustionActivityStatus.inbox.value,
              questionSends.sent_from_user!,
              questionSends.sent_to_user!);*/
          // debugPrint(
          //     "questionSends.sent_from_user_id ${questionSends.sent_from_user_id}");
          if (questionSends.sent_from_user_id != null &&
              questionSends.sent_from_user_id !=
                  VideoTC().currentUserProfile!.id!) {
            VideoTC().allInboxQuestionList.add(questionSends);
          }
        }
        /*parentClass.setState(() {
          questionInboxListStatus = LoadingStatus.complete;
        });*/
        await UserProfileBloc().getRecoverConnectionList();
        questionInboxListStatus = LoadingStatus.complete;
        debugPrint("questionInboxListStatus $questionInboxListStatus");
      } else {
        questionInboxListStatus = LoadingStatus.error;
      }
    }
  }

  getOutboxQuestionsList(parentClass) async {
    if (questionOutboxListStatus == LoadingStatus.notStarted) {
      questionOutboxListStatus = LoadingStatus.loading;

      // var questionOutBoxUrl =
      //     "${baseApiUrl}inbox-outbox?sent_from_user_id=${VideoTC().currentUserProfile!.id}";
      // var questionOutBoxUrl =
      //     "${baseApiUrl}question-send?sent_from_user_id=${VideoTC().currentUserProfile!.id}";
      var questionOutBoxUrl =
          "${baseApiUrl}question-send?sent_from_user_id=${VideoTC().currentUserProfile!.id}&include=video.question,question.tags,video.owner";
      debugPrint("questionOutBoxUrl $questionOutBoxUrl");

      Response? response = await ApiUrlResponse.apiRequestContext(
          parentClass.context, HttpRequestType.get, questionOutBoxUrl,
          isLoading: false);

      if (response!.statusCode == 200) {
        Map<String, dynamic> jsonData = json.decode(response.body);
        List<dynamic> allData = jsonData["data"];

        // debugPrint("jsonData $jsonData");
        // debugPrint("jsonData ${allData.length}");
        VideoTC().allOutboxQuestionList.clear();
        for (int i = 0; i < allData.length; i++) {
          Map<String, dynamic> json = allData[i];
          QuestionSends questionSends = QuestionSends.fromMap(json);

          if (questionSends.question!.createdBy != null) {
            createCustomQuestionActivity(
              questionSends.question!,
              QustionActivityStatus.custom.value,
              questionSends.question!.createdBy,
              questionSends.question!.createdBy,
              isCustomQuestion: true,
            );
          }

          List<QuestionActivity>? questionActivities =
              json.containsKey("question_activities")
                  ? (json['question_activities'] as List)
                      .map((data) => QuestionActivity.fromMap(data))
                      .toList()
                  : [];

          for (int j = 0; j < questionActivities.length; j++) {
            QuestionActivity questionActivity = questionActivities[j];
            DateTime dateTime = DateTime.parse(questionActivity.created_at!);
            questionActivity.date = dateTime.millisecondsSinceEpoch;
            questionActivity.activity_text =
                setQuestionActivityText(questionActivity);
            questionSends.question!.questionActivities!.add(questionActivity);
          }
          /*for (int j = 0;
              j < questionSends.question!.questionActivities!.length;
              j++) {
            QuestionActivity questionActivity =
                questionSends.question!.questionActivities![j];
            DateTime dateTime = DateTime.parse(questionActivity.created_at!);
            questionActivity.date = dateTime.millisecondsSinceEpoch;
            questionActivity.activity_text =
                setQuestionActivityText(questionActivity);
          }*/

          // questionSends.show();
          // debugPrint("getOutboxQuestionsList ${questionSends.id}");
          VideoTC().allOutboxQuestionList.add(questionSends);
        }
        parentClass.setState(() {
          questionOutboxListStatus = LoadingStatus.complete;
        });
      } else {
        questionOutboxListStatus = LoadingStatus.error;
      }
    }
  }

  sendQuestion(parentClass, UserProfile sentToUser,
      UserRelationship userRelationship, Questions questions,
      {bool isLoading = true}) async {
    var sendQuestionUrl = "${baseApiUrl}question-send";
    DateTime now = DateTime.now();
    DateFormat formatter = DateFormat('yyyy-MM-dd');
    String formattedDate = formatter.format(now);

    var body;
    if (userRelationship.connection_request_status ==
        ConnectionStatus.invited.value) {
      body = json.encode({
        "question_id": questions.id,
        "invitation_id": userRelationship.invitation_id!,
        "sent_at": formattedDate
      });
    } else {
      body = json.encode({
        "question_id": questions.id,
        "sent_to_user_id": sentToUser.id!,
        "sent_at": formattedDate
      });
    }

    debugPrint("sendQuestionUrl $sendQuestionUrl");
    debugPrint("body $body");

    Response? response = await ApiUrlResponse.apiRequestContext(
        parentClass.context, HttpRequestType.post, sendQuestionUrl,
        body: body, isLoading: isLoading);
    if (response != null) {
      debugPrint("response.statusCode ${response.statusCode}");

      if (response.statusCode == 201) {
        Map<String, dynamic> jsonData = json.decode(response.body);
        QuestionSends questionSends = QuestionSends.fromMap(jsonData["data"]);
        Future.delayed(Duration.zero, () async {
          if (VideoTC().allOutboxQuestionList.isEmpty) {
            VideoTC().allOutboxQuestionList.add(questionSends);
          } else {
            VideoTC().allOutboxQuestionList.insert(0, questionSends);
          }

          createCustomQuestionActivity(
            questions,
            QustionActivityStatus.sent.value,
            VideoTC().currentUserProfile,
            sentToUser,
          );

          createCustomQuestionActivity(
            questions,
            QustionActivityStatus.received.value,
            VideoTC().currentUserProfile,
            sentToUser,
          );

          for (int i = 0; i < VideoTC().allOutboxQuestionList.length; i++) {
            QuestionSends outboxQuestionSends =
                VideoTC().allOutboxQuestionList[i];
            if (outboxQuestionSends.question!.id ==
                questionSends.question!.id) {
              outboxQuestionSends.question!.questionActivities = [];
              // outboxQuestionSends.question!.questionActivities!
              //     .addAll(questions.questionActivities!);
            }
          }
          debugPrint(
              "VideoTC().allOutboxQuestionList ${VideoTC().allOutboxQuestionList.length}");
          parentClass.setRecipientQuestionSendList(questionSends);
          parentClass.showQuestionSentAlert(questions);
        });
      } else {
        Navigator.of(parentClass.context).pop();
        showAPIDialog(
            parentClass.context, "Sending Question Failed. Please try again.");
      }
    } else {
      showAPIDialog(
          parentClass.context, "Sending Question Failed. Please try again.");
    }
  }

  deleteQustionSend(parentClass, QuestionSends questionSends) async {
    var deleteQuestionDelUrl = "${baseApiUrl}question-send/${questionSends.id}";
    debugPrint("deleteQuestionDelUrl $deleteQuestionDelUrl");

    Response? response = await ApiUrlResponse.apiRequestContext(
      parentClass.context,
      HttpRequestType.del,
      deleteQuestionDelUrl,
    );
    if (response != null) {
      debugPrint("response ${response.reasonPhrase}");
      debugPrint("response.statusCode ${response.statusCode}");
      if (response.statusCode == 204) {
        parentClass.deleteQustionDone();
      } else {
        showAPIDialog(parentClass.context, "Question delete Failed.");
      }
    } else {
      showAPIDialog(parentClass.context, "Question delete Failed.");
    }
  }

  resendQustionSend(parentClass, QuestionSends questionSends) async {
    var resendQuestionDelUrl = "${baseApiUrl}question-send/${questionSends.id}";
    debugPrint("resendQustionSend $resendQuestionDelUrl");
    var body = json.encode({
      "question_status": QustionActivityStatus.resent.value,
    });

    Response? response = await ApiUrlResponse.apiRequestContext(
        parentClass.context, HttpRequestType.put, resendQuestionDelUrl,
        body: body);

    if (response != null) {
      debugPrint("response ${response.reasonPhrase}");
      debugPrint("response.statusCode ${response.statusCode}");

      if (response.statusCode == 200) {
        // UserProfile? sentToUser = await VideoTC()
        //     .accountBloc!
        //     .getUserProfile(questionSends.sent_to_user_id!);
        /*if (questionSends.sent_to_user == null) {
          debugPrint("questionSends.sent_to_user --- null");
        } else {
          debugPrint("questionSends.sent_to_user --- not null");
        }
        questionSends.sent_to_user!.show();*/
        questionSends.updated_at = DateTime.now().toString();
        createCustomQuestionActivity(
          questionSends.question!,
          QustionActivityStatus.resent.value,
          VideoTC().currentUserProfile,
          questionSends.sent_to_user!,
        );
        for (int i = 0; i < VideoTC().allOutboxQuestionList.length; i++) {
          QuestionSends outboxQuestionSends =
              VideoTC().allOutboxQuestionList[i];
          if (outboxQuestionSends.id != questionSends.id &&
              outboxQuestionSends.question!.id == questionSends.question!.id) {
            outboxQuestionSends.question!.questionActivities = [];
            outboxQuestionSends.question!.questionActivities!
                .addAll(questionSends.question!.questionActivities!);
          }
          outboxQuestionSends.question!.isExpanded = false;
        }
        parentClass.resendQustionSendDone();
      } else {
        Navigator.of(parentClass.context).pop();
        showAPIDialog(parentClass.context, "Question resend Failed.");
      }
    } else {
      Navigator.of(parentClass.context).pop();
      showAPIDialog(parentClass.context, "Question resend Failed.");
    }
  }

  Future<Video> sendQuestionWithVideo(parentClass, int videoId) async {
    debugPrint("sendQuestionWithVideo $videoSentUserId");
    debugPrint("videoSentUserId ${VideoTC().currentUserProfile!.id}");
    Video video = Video();
    bool isVideoDone = false;
    if (videoSentUserId == VideoTC().currentUserProfile!.id) {
      var questionSendUrl = "${baseApiUrl}question-send";
      debugPrint("questionSendUrl $questionSendUrl");
      DateTime now = DateTime.now();
      DateFormat formatter = DateFormat('yyyy-MM-dd');
      String formattedDate = formatter.format(now);
      debugPrint("formattedDate $formattedDate");

      var body = json.encode({
        "question_id": questionId,
        "video_id": videoId,
        "sent_to_user_id": VideoTC().currentUserProfile!.id,
        "sent_at": formattedDate,
      });
      debugPrint("body $body");

      Response? response = await ApiUrlResponse.apiRequestContext(
          parentClass.context, HttpRequestType.post, questionSendUrl,
          body: body, isLoading: false);
      if (response != null) {
        debugPrint("response.statusCode ${response.body}");
        debugPrint("response.statusCode ${response.statusCode}");
        if (response.statusCode == 201) {
          Map<String, dynamic> jsonData = json.decode(response.body);

          if (jsonData["data"] != null) {
            debugPrint("questionSendsToMySelf------1");
            questionSendsToMySelf = QuestionSends.fromMap(jsonData["data"]);
          }
          if (jsonData["data"]["video"] != null) {
            video = Video.fromMap(jsonData["data"]["video"]);
            isVideoDone = true;
          }
        } /*else {
          showAPIDialog(parentClass.context, "Video upload Failed");
        }*/
      } /*else {
        showAPIDialog(parentClass.context, "Video upload Failed");
      }*/
    } else {
      isVideoDone = true;
    }
    if (isVideoDone) {
      for (int i = 0; i < VideoTC().allInboxQuestionList.length; i++) {
        QuestionSends questionSends = VideoTC().allInboxQuestionList[i];
        if (questionSends.question_id == questionId) {
          var questionSendUrl =
              "${baseApiUrl}question-send/${questionSends.id}}";
          debugPrint("questionSendUrl $questionSendUrl");
          var body = json.encode({
            "question_status": QustionActivityStatus.answered.value,
            "video_id": videoId,
          });
          debugPrint("questionSendUrl body $body");

          Response? response = await ApiUrlResponse.apiRequestContext(
              parentClass.context, HttpRequestType.put, questionSendUrl,
              body: body, isLoading: false);
          debugPrint("response $response");
          if (response != null) {
            debugPrint("response.statusCode ${response.body}");
            debugPrint("response.statusCode ${response.statusCode}");

            for (int i = 0; i < VideoTC().allOutboxQuestionList.length; i++) {
              QuestionSends outboxQuestionSends =
                  VideoTC().allOutboxQuestionList[i];
              if (outboxQuestionSends.question!.id ==
                  questionSendsToAnswser!.question!.id) {
                createCustomQuestionActivity(
                  outboxQuestionSends.question!,
                  QustionActivityStatus.answered.value,
                  null,
                  VideoTC().currentUserProfile,
                );
              }
            }

            Map<String, dynamic> jsonData = json.decode(response.body);

            if (jsonData["data"]["video"] != null) {
              video = Video.fromMap(jsonData["data"]["video"]);
              questionSends.video = video;
              questionSends.video_id = video.id;
              questionSends.video!.question = questionSends.question;
              questionSends.video!.owner =
                  Owner.fromUserProfile(VideoTC().currentUserProfile!);
            }
          }
        }
      }
    }
    return video;
  }

  getQuestionSendList(parentClass, int sent_to_user_id,
      {bool isInvited = false, bool isLoading = true}) async {
    questionSendListStatus = LoadingStatus.notStarted;
    var allQuestionSendlUrl;
    if (isInvited == false) {
      allQuestionSendlUrl =
          "${baseApiUrl}question-send?sent_to_user_id=$sent_to_user_id&include=video.question,question.tags,video.owner";
    } else {
      allQuestionSendlUrl =
          "${baseApiUrl}question-send?invitation_id=$sent_to_user_id&include=video.question,question.tags,video.owner";
    }

    debugPrint("allQuestionSendlUrl $allQuestionSendlUrl");
    if (isLoading) {
      innerAuthLoader(parentClass.context);
    }
    Response? response = await ApiUrlResponse.apiRequestContext(
        parentClass.context, HttpRequestType.get, allQuestionSendlUrl,
        isLoading: false);
    if (response != null) {
      // Navigator.of(parentClass.context).pop();
      debugPrint("response ${response.reasonPhrase}");
      debugPrint("response.statusCode ${response.statusCode}");
      if (response!.statusCode == 200) {
        Map<String, dynamic> jsonData = json.decode(response.body);
        List<dynamic> allData = jsonData["data"];

        if (isInvited == false) {
          if (sent_to_user_id != VideoTC().currentUserProfile!.id!) {
            await getCustomQuestionsList(parentClass, sent_to_user_id);
          }
        }

        parentClass.recipientQuestionSendList.clear();
        for (int i = 0; i < allData.length; i++) {
          QuestionSends questionSends = QuestionSends.fromMap(allData[i]);
          parentClass.recipientQuestionSendList.add(questionSends);
        }
        if (sent_to_user_id == VideoTC().currentUserProfile!.id) {
          parentClass.currentUserQuestionSendList.clear();
          for (int i = 0; i < allData.length; i++) {
            QuestionSends questionSends = QuestionSends.fromMap(allData[i]);
            parentClass.currentUserQuestionSendList.add(questionSends);
          }
        }

        parentClass.recipientQuestionListLoaded();
        if (isLoading) {
          Navigator.of(parentClass.context).pop();
          questionSendListStatus = LoadingStatus.complete;
        }
      } else {}
    } else {
      if (isLoading) {
        Navigator.of(parentClass.context).pop();
        questionSendListStatus = LoadingStatus.complete;
      }
      showAPIDialog(parentClass.context, "Network Problem.");
    }
  }

  discardQustionSend(parentClass, QuestionSends questionSends) async {
    var discardQuestionDelUrl =
        "${baseApiUrl}question-sends/${questionSends.id}/discard";
    debugPrint("discardQuestionDelUrl $discardQuestionDelUrl");

    Response? response = await ApiUrlResponse.apiRequestContext(
      parentClass.context,
      HttpRequestType.patch,
      discardQuestionDelUrl,
    );
    if (response != null) {
      debugPrint("response ${response.reasonPhrase}");
      debugPrint("response.statusCode ${response.statusCode}");
      if (response.statusCode == 200) {
        parentClass.discardQustionSendDone(questionSends);
      } else {
        showAPIDialog(parentClass.context, "Question discard Failed.");
      }
    } else {
      showAPIDialog(parentClass.context, "Question discard Failed.");
    }
  }

  revocerQustionSend(parentClass, QuestionSends questionSends) async {
    var recoverQuestionDelUrl =
        "${baseApiUrl}question-send/${questionSends.id}";
    debugPrint("recoverQuestionDelUrl $recoverQuestionDelUrl");
    var body = json.encode({
      "trashed_at": null,
    });

    Response? response = await ApiUrlResponse.apiRequestContext(
      parentClass.context,
      HttpRequestType.put,
      recoverQuestionDelUrl,
      body: body,
    );
    if (response != null) {
      debugPrint("response ${response.reasonPhrase}");
      debugPrint("response.statusCode ${response.statusCode}");
      if (response.statusCode == 200) {
        parentClass.recoverQustionSendDone(questionSends);
      } else {
        showAPIDialog(parentClass.context, "Question recover Failed.");
      }
    } else {
      showAPIDialog(parentClass.context, "Question recover Failed.");
    }
  }

  void dispose() {
    customQuestionController.dispose();
  }
}
