import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:legacylock_app/data/api/user_profile_bloc.dart';
import 'package:legacylock_app/data/model/user_profile.dart';
import 'package:legacylock_app/data/model/user_relationship.dart';
import 'package:legacylock_app/data/model/users.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart';
import 'package:legacylock_app/helper/consts/colors.dart';
import 'package:legacylock_app/helper/consts/urls.dart';
import 'package:legacylock_app/helper/network/api_response.dart';
import 'package:legacylock_app/helper/util/utility_class.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:legacylock_app/pages/main_page.dart';
import 'package:legacylock_app/router/route_constants.dart';
import 'package:http/http.dart' as http;
// ignore: depend_on_referenced_packages
import 'package:http_parser/http_parser.dart';

class AccountBloc {
  final GlobalKey<FormFieldState> firstNameFormFieldKey = GlobalKey();
  final GlobalKey<FormFieldState> middleNameFormFieldKey = GlobalKey();
  final GlobalKey<FormFieldState> lastNameFormFieldKey = GlobalKey();

  var firstNameController = TextEditingController();
  var middleNameController = TextEditingController();
  var lastNameController = TextEditingController();

  ///this function is used to logout from the app
  logout(context) async {
    debugPrint("logout----------mahfuj");
    if (await Utility.instance.checkConnection()) {
      await VideoTC().logout();
      // Navigator.of(context).pop();
      setBottomTabIndex.sink.add(0);
      Navigator.of(context).pushNamedAndRemoveUntil(
          welcomeRoute, (Route<dynamic> route) => false);
    } else {
      showAPIDialog(context, "No internet connection");
    }
  }

  // ব্যবহারকারীর লগইন প্রক্রিয়া সম্পন্ন করে এবং VideoTC সিঙ্গেলটন অবজেক্টে ডেটা সংরক্ষণ করে
  authenticateUser(parentClass, phoneNumber, password,
      {bool isLoading = true}) async {
    if (isLoading) {
      innerAuthLoader(parentClass.context);
    }
    var loginUrl = "${baseApiUrl}login";
    debugPrint("authenticateUser $loginUrl");

    var body = json.encode({"phone_number": phoneNumber, "password": password});

    Response? response;
    if (isLoading) {
      response = await ApiUrlResponse.apiRequestContext(
          parentClass.context, HttpRequestType.post, loginUrl,
          body: body, isAccessToken: false, isLoading: false);
    } else {
      response = await ApiUrlResponse.apiRequestContext(
          parentClass, HttpRequestType.post, loginUrl,
          body: body, isAccessToken: false, isLoading: false);
    }
    if (response != null) {
      debugPrint("response.statusCode ${response.statusCode}");
      if (response.statusCode == 200) {
        Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint("AccessToken ${jsonData["AccessToken"]}");

        // এক্সেস টোকেন SharedPreferences এ সংরক্ষণ করা হয়
        await VideoTC().saveAccesToken(jsonData["AccessToken"]);
        
        // VideoTC সিঙ্গেলটন অবজেক্টে ব্যবহারকারীর তথ্য সেট করা হয়
        // এটি User অবজেক্ট তৈরি করে এবং setCurrentUserFromUserId মেথড কল করে
        await VideoTC().setCurrentUser(jsonData["user"], phoneNumber, password);

        debugPrint("VideoTC ${await VideoTC().getAccesToken()}");
        if (isLoading) {
          // মেইন রাউট লোড করা - এটি প্রশ্ন, ট্যাগ, ইনবক্স/আউটবক্স ডেটা লোড করে
          await VideoTC().loadMainRoute(parentClass.context);
          // লগইন ইভেন্ট লগ করা (অ্যানালিটিক্স)
          await VideoTC().setLogEvent(LogEvent.login);
        }
      } else if (response.statusCode == 401) {
        // লগইন ব্যর্থ হলে ডিফল্ট মান সেট করা
        await VideoTC().saveUserId(DefaultValue.kDefaultInt,
            DefaultValue.kDefaultString, DefaultValue.kDefaultString);
        if (isLoading) {
          Navigator.of(parentClass.context).pop();
          Navigator.of(parentClass.context).pop();
          String strErrorMessage =
              json.decode(response.body).containsKey("message")
                  ? json.decode(response.body)['message']
                  : "";
          if (strErrorMessage == "User is not confirmed.") {
            debugPrint("strErrorMessage----");
            showAPIDialog(parentClass.context, "User not exits.");
          } else {
            showErrorPasswordDialog(parentClass, strErrorMessage);
          }
        } else {
          // Navigator.of(parentClass).pop();
        }
      } else {
        await VideoTC().saveUserId(DefaultValue.kDefaultInt,
            DefaultValue.kDefaultString, DefaultValue.kDefaultString);
      }
    }
  }

  checkUserNumber(parentClass, phoneNumber) async {
    var checkPhoneNumberUrl = "${baseApiUrl}find-by-phone-number";
    debugPrint("checkPhoneNumberUrl $checkPhoneNumberUrl");

    var body = json.encode({
      "phone_number": phoneNumber,
    });

    Response? response = await ApiUrlResponse.apiRequestContext(
        parentClass.context, HttpRequestType.post, checkPhoneNumberUrl,
        body: body, isAccessToken: false, isLoading: false);
    if (response != null) {
      // debugPrint("response.body----- ${response.body}");
      // debugPrint("response.statusCode ${response.statusCode}");
      if (response.statusCode == 200) {
        // String message = json.decode(response.body).containsKey("message")
        //     ? json.decode(response.body)['message']
        //     : "";
        // debugPrint("message $message");
        parentClass.loadUserExistsInfo(true);
      } else {
        parentClass.loadUserExistsInfo(false);
      }
    } else {
      Navigator.of(parentClass.context).pop();
      showAPIDialog(parentClass.context, "There is a connection problem");
    }
  }

  showErrorPasswordDialog(parentClass, message) {
    showCupertinoDialog(
      context: parentClass.context,
      barrierDismissible: true,
      builder: (context) {
        return CupertinoAlertDialog(
          content: Text(
            message,
            style: TextStyle(
                // color: kBlack,
                fontFamily: getFontFamily(),
                fontWeight: FontWeight.w500,
                fontSize: 16),
          ),
          actions: [
            CupertinoDialogAction(
                child: createText("OK", 15, fontWeight: FontWeight.w500),
                onPressed: () {
                  Navigator.of(context).pop();
                  parentClass.passwordWrong();
                }),
          ],
        );
      },
    );
  }

  ///This method is used to get all data of user from cloud
  Future<User?> getUserData(int id) async {
    var userUrl = "${baseApiUrl}user/$id";
    debugPrint("userUrl $userUrl");

    Response? response =
        await ApiUrlResponse.apiRequest(HttpRequestType.get, userUrl);
    // debugPrint("userUrl response statusCode ${response!.statusCode}");
    // debugPrint("userUrl response body${response.body}");
    if (response!.statusCode == 401) {
      return null;
    }
    Map<String, dynamic> jsonData = json.decode(response.body);
    if (jsonData["data"] != null) {
      return User.fromMap(jsonData["data"]);
    }
    return null;
  }

  // ব্যবহারকারীর প্রোফাইল তথ্য লোড করে - প্রথমে VideoTC সিঙ্গেলটন অবজেক্ট থেকে, না পেলে API কল করে
  Future<UserProfile?> getUserProfile(int id) async {
    // এক্সেস টোকেন চেক করে লগইন স্ট্যাটাস নির্ধারণ করা
    String accessToken = await VideoTC().getAccesToken();
    bool isLoggedIn = accessToken != DefaultValue.kDefaultString ? true : false;
    if (isLoggedIn) {
      // যদি ইতিমধ্যে VideoTC সিঙ্গেলটন অবজেক্টে currentUserProfile লোড করা থাকে
      if (VideoTC().currentUserProfile != null &&
          VideoTC().currentUserProfile!.id != null) {
        UserProfile currentUserProfile = VideoTC().currentUserProfile!;

        // যদি অনুরোধকৃত আইডি বর্তমান ব্যবহারকারীর আইডি হয়, তাহলে ক্যাশ করা প্রোফাইল রিটার্ন করা
        if (id == currentUserProfile.id) {
          return currentUserProfile;
        } else {
          // toUserRelationshipsDetails থেকে সম্পর্কিত ব্যবহারকারীর তথ্য খুঁজে বের করা
          for (int i = 0;
              i < currentUserProfile.toUserRelationshipsDetails!.length;
              i++) {
            UserRelationship userRelationship =
                currentUserProfile.toUserRelationshipsDetails![i];
            if (userRelationship.id == id) {
              // সম্পর্কিত ব্যবহারকারীর প্রোফাইল তৈরি করে রিটার্ন করা
              return UserProfileBloc().createUserProfile(
                  userRelationship.id!,
                  userRelationship.first_name!,
                  userRelationship.middle_name!,
                  userRelationship.last_name!,
                  storyCount: userRelationship.stories,
                  familyCount: userRelationship.total_family_count,
                  profile_pic: userRelationship.profile_pic,
                  created_at: userRelationship.created_at,
                  updated_at: userRelationship.updated_at,
                  userType: 0);
            }
          }
          // fromUserRelationshipsDetails থেকে সম্পর্কিত ব্যবহারকারীর তথ্য খুঁজে বের করা
          for (int i = 0;
              i < currentUserProfile.fromUserRelationshipsDetails!.length;
              i++) {
            UserRelationship userRelationship =
                currentUserProfile.fromUserRelationshipsDetails![i];
            if (userRelationship.id == id) {
              // সম্পর্কিত ব্যবহারকারীর প্রোফাইল তৈরি করে রিটার্ন করা
              return UserProfileBloc().createUserProfile(
                userRelationship.id!,
                userRelationship.first_name!,
                userRelationship.middle_name!,
                userRelationship.last_name!,
                storyCount: userRelationship.stories,
                familyCount: userRelationship.total_family_count,
                profile_pic: userRelationship.profile_pic,
                created_at: userRelationship.created_at,
                updated_at: userRelationship.updated_at,
                userType: 0,
                invitation_id: userRelationship.invitation_id,
              );
            }
          }
        }
      }
    }
    var userProfileUrl = "${baseApiUrl}user/$id/profile";
    debugPrint("userUrl $userProfileUrl");
    Response? response =
        await ApiUrlResponse.apiRequest(HttpRequestType.get, userProfileUrl);

    if (response != null) {
      // debugPrint("response statusCode ${response.statusCode}");
      // debugPrint("response body${response.body}");
      if (response.statusCode == 401 || response.statusCode == 404) {
        return null;
      }
      Map<String, dynamic> jsonData = json.decode(response.body);
      return UserProfile.fromMap(jsonData["data"]);
    }
    return null;
  }

  updateUserInfo(context) async {
    bool firstNameValidate = firstNameFormFieldKey.currentState!.validate();
    bool middleNameValidate = middleNameFormFieldKey.currentState!.validate();
    bool lastNameValidate = lastNameFormFieldKey.currentState!.validate();

    if (!firstNameValidate || !middleNameValidate || !lastNameValidate) {
    } else {
      String updateApiUrl =
          "${baseApiUrl}user/${VideoTC().currentUserProfile!.id}";
      debugPrint("updateApiUrl $updateApiUrl");
      var body = json.encode({
        "first_name": firstNameController.text.trim(),
        "middle_name": middleNameController.text.trim(),
        "last_name": lastNameController.text.trim(),
      });

      debugPrint("body $body");

      Response? response = await ApiUrlResponse.apiRequestContext(
          context, HttpRequestType.put, updateApiUrl,
          body: body);

      if (response != null) {
        debugPrint("doUserRegistration response.body ${response.body}");
        debugPrint("response ${response.statusCode}");

        if (response.statusCode == 200) {
          Map<String, dynamic> jsonData = json.decode(response.body);
          debugPrint("jsonData $jsonData");
          VideoTC videoTC = VideoTC();
          videoTC.currentUserProfile!.first_name = videoTC
              .currentUserProfile!.first_name = firstNameController.text.trim();
          videoTC.currentUserProfile!.middle_name = videoTC.currentUserProfile!
              .middle_name = middleNameController.text.trim();
          videoTC.currentUserProfile!.last_name = videoTC
              .currentUserProfile!.last_name = lastNameController.text.trim();

          Utility.instance.hideKeyboard(context);
        } else {
          showAPIDialog(context, "Upload User Info Failed.");
        }
      } else {
        showAPIDialog(context, "Upload User Info Failed.");
      }
    }
  }

  uploadProfilePic(CroppedFile file, parentClass, context, userId, fromSignup,
      String mimeType) async {
    innerAuthLoader(context);
    var uploladPhotoUrl = "${baseApiUrl}profile-pic";
    debugPrint("uploladPhotoUrl   $uploladPhotoUrl");
    var uri = Uri.parse(uploladPhotoUrl);
    File image = File(file.path);
    var request = http.MultipartRequest("POST", uri);
    debugPrint("userId $userId");
    request.fields['user_id'] = userId.toString();
    request.files.add(
      http.MultipartFile.fromBytes(
        "profile_pic",
        image.readAsBytesSync(),
        filename: "Photo.png",
        contentType: MediaType("image", "png"),
      ),
    );
    var accesToken = await VideoTC().getAccesToken();
    request.headers.addAll({"Authorization": "Bearer $accesToken"});

    var response = await request.send();
    debugPrint("uploladPhotoUrl response   $response");
    debugPrint("uploladPhotoUrl response.statusCode   ${response.statusCode}");

    response.stream.transform(utf8.decoder).listen((value) {
      debugPrint("uploladPhotoUrl value $value");
      if (response.statusCode == 200) {
        Map<String, dynamic> jsonData = json.decode(value);
        debugPrint("jsonData[] ${jsonData["data"]}");
        User user = User.fromMap(jsonData["data"]);
        VideoTC().currentUserProfile!.profile_pic = user.profile_pic;
        if (parentClass != null) {
          parentClass.editCallBack();
          if (parentClass.mounted()) {
            parentClass.setState(() {});
          }
        }
      }
    });
    Navigator.of(context).pop();
    if (response.statusCode == 200) {
      // Track successful profile picture upload only during signup
      if (fromSignup) {
        debugPrint("Tracking profile picture screen: uploaded");
        await VideoTC().setLogEvent(LogEvent.passProfilePicScreen, isUploaded: true);
      }
      
      if (fromSignup == true) {
        // showImageUploadDialog(
        //     context, true, "Account Create Successfully", fromSignup);

        await VideoTC().loadMainRoute(context, isLoading: true);
      } else {
        showImageUploadDialog(
            context, true, "Picture Uploaded Successfully", fromSignup);
        setBottomTabIndex.sink.add(6);
      }
    } else if (response.statusCode == 413) {
      showImageUploadDialog(context, false, "File size too large", fromSignup);
    } else {
      showImageUploadDialog(
          context, false, "Picture Uploaded failed", fromSignup);
    }
  }

  showImageUploadDialog(context, isUploaded, message, fromSignup) {
    showCupertinoDialog(
      context: context,
      barrierDismissible: true,
      builder: (buildContext) {
        return CupertinoAlertDialog(
          content: Text(
            message,
            style: TextStyle(
                // color: kBlack,
                fontFamily: getFontFamily(),
                fontWeight: FontWeight.w500,
                fontSize: 16),
          ),
          actions: [
            CupertinoDialogAction(
                child: Text(
                  "OK",
                  style: TextStyle(
                      fontFamily: getFontFamily(),
                      color: kBlack,
                      fontWeight: FontWeight.w500,
                      fontSize: 15),
                ),
                onPressed: () async {
                  if (fromSignup) {
                    Navigator.of(context).pop();
                    await VideoTC().loadMainRoute(context, isLoading: true);
                  } else {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                    // VideoTC().currentUser = (await VideoTC()
                    //     .accountBloc!
                    //     .getUserData(VideoTC().currentUser.id!))!;
                  }
                }),
          ],
        );
      },
    );
  }

  deleteAccount(parentClass) async {
    var deleteUrl = "${baseApiUrl}user/${VideoTC().currentUserProfile!.id!}";
    debugPrint("deleteUrl $deleteUrl");

    Response? response = await ApiUrlResponse.apiRequestContext(
      parentClass.context,
      HttpRequestType.del,
      deleteUrl,
    );
    if (response != null) {
      debugPrint("response.body ${response.body}");
      debugPrint("response.statusCode ${response.statusCode}");
      if (response.statusCode == 204) {
        parentClass.accountDeletionDoneAlert();
      } else {
        Navigator.of(parentClass.context).pop();
        showAPIDialog(
            parentClass.context, "Deletion is not possible, try again later");
      }
    } else {
      Navigator.of(parentClass.context).pop();
      showAPIDialog(parentClass.context, "There is a connection problem");
    }
  }
}
