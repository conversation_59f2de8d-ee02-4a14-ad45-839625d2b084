import 'package:flutter/material.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/consts/colors.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:legacylock_app/pages/main_page.dart';
import 'package:legacylock_app/pages/tabs/ProfileTab/profile_tab.dart';

class BottomTabBar extends StatelessWidget {
  final TabController tabController;
  final bool isNotificationEnable;
  final bool isNotificationQustionUnlock;
  static int oldIndex = 0;

  const BottomTabBar({
    Key? key,
    required this.tabController,
    required this.isNotificationEnable,
    required this.isNotificationQustionUnlock,
  }) : super(key: key);

  @override
  /// Builds the bottom tab bar for navigation.
  ///
  /// This widget displays a tab bar at the bottom of the screen with four
  /// tabs: Home, Question, People, and Profile. The Home tab is represented
  /// by a home icon, the Question tab is represented by a video camera icon,
  /// the People tab is represented by a people icon, and the Profile tab is
  /// represented by a user profile picture icon. When a tab is tapped, the
  /// [tabController] is notified. The `onTap` callback is used to notify the
  /// [tabController] when a tab is tapped. The [isNotificationEnable] and
  /// [isNotificationQustionUnlock] are used to determine if a notification
  /// badge should be displayed on the Home and Question tabs, respectively.
  ///
  Widget build(BuildContext context) {
    double iconSize = 40;
    // debugPrint("isNotificationEnable $isNotificationEnable");
    return Container(
      height: 60 + MediaQuery.of(context).padding.bottom,
      decoration: const BoxDecoration(
        color: kLightBlue,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.black.withOpacity(0.1),
        //     spreadRadius: 6,
        //     blurRadius: 12,
        //     offset: const Offset(0, 0),
        //   ),
        // ],
      ),
      child: TabBar(
        controller: tabController,
        indicatorColor: Colors.white,
        unselectedLabelColor: const Color.fromARGB(150, 241, 234, 245),
        labelColor: Colors.white,

        // labelStyle: TextStyle(fontSize: 13, fontFamily: getFontFamily()),
        // padding: const EdgeInsets.symmetric(horizontal: 6),
        onTap: (index) {
          if (index == 0) {
            setBottomTabIndexChange.sink.add(0);
          } else if (index == 1 && oldIndex == index) {
            // resetTabRecipient!.sink.add(0);
          } else if (oldIndex == 3) {
            ProfileTabState.getInstance().loadUserProfileTab(-1);
          }
          oldIndex = index;
        },
        tabs: [
          Tab(
            key: keyBottomItem1,
            // text: "", //Home
            icon: Stack(
              alignment: Alignment.center,
              children: [
                Icon(
                  Icons.home_rounded,
                  size: iconSize,
                ),
                Visibility(
                  visible: isNotificationEnable == true,
                  child: Positioned(
                    right: 0,
                    top: 25,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: darkRed,
                        border: Border.fromBorderSide(
                          BorderSide(color: Colors.white, width: 2),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          Tab(
            key: keyBottomItem2,
            // text: "", //Question

            icon: Stack(
              children: [
                Icon(
                  Icons.videocam_outlined,
                  size: iconSize,
                ),
                Visibility(
                  visible: isNotificationQustionUnlock == true,
                  child: Positioned(
                    right: 12,
                    top: 25,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: darkRed,
                        border: Border.fromBorderSide(
                          BorderSide(color: Colors.white, width: 2),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          Tab(
            key: keyBottomItem3,
            // text: "", //Profile
            icon: Icon(
              Icons.people_alt_rounded,
              size: iconSize,
            ),
          ),
          Tab(
            key: keyBottomItem4,
            //iconMargin: const EdgeInsets.only(bottom: 5),
            icon: VideoTC().currentUserProfile!.id != null
                ? getUserIcon(VideoTC().currentUserProfile!.id!, "", "",
                    isGoProfile: false, size: 35)
                : Icon(
                    Icons.account_circle_rounded,
                    size: iconSize,
                  ),
          ),
        ],
      ),
    );
  }
}
