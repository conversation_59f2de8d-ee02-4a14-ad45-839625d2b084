import 'dart:convert';
import 'dart:io';

import 'package:courier_flutter/courier_flutter.dart';
import 'package:courier_flutter/models/courier_inbox_listener.dart';
import 'package:courier_flutter/models/courier_push_listener.dart';
import 'package:event_bus/event_bus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:legacylock_app/data/api/question_bloc.dart';
import 'package:legacylock_app/data/api/user_profile_bloc.dart';
import 'package:legacylock_app/data/model/notification.dart';
import 'package:legacylock_app/data/model/question_sends.dart';
import 'package:legacylock_app/data/model/questions.dart';
import 'package:legacylock_app/data/model/relation.dart';
import 'package:legacylock_app/data/model/relationships.dart';
import 'package:legacylock_app/data/model/tags.dart';
import 'package:legacylock_app/data/model/user_profile.dart';
import 'package:legacylock_app/data/model/user_relationship.dart';
import 'package:legacylock_app/data/model/users.dart';
import 'package:legacylock_app/helper/Notification/notification_tc.dart';
import 'package:legacylock_app/helper/Notification/push_notification_util.dart';
import 'package:legacylock_app/helper/app_start/application.dart';
import 'package:legacylock_app/helper/consts/app_constants.dart';
import 'package:legacylock_app/helper/consts/urls.dart';
import 'package:legacylock_app/helper/util/analytics_events.dart';
import 'package:legacylock_app/helper/util/shared_preference.dart';
import 'package:legacylock_app/data/api/account_bloc.dart';
import 'package:legacylock_app/helper/util/utility_class.dart';
import 'package:legacylock_app/helper/util/view_util.dart';
import 'package:legacylock_app/pages/main_page.dart';
import 'package:legacylock_app/router/route_constants.dart';
import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:permission_handler/permission_handler.dart';

enum LoadingStatus { notStarted, loading, complete, error }

class VideoTC implements Application {
  EventBus? eventBus;
  bool? contactPermission;
  AccountBloc? accountBloc;

  QuestionBloc? questionBloc;
  UserProfile? currentUserProfile = UserProfile();
  List<Questions> allQuestionList = [];
  List<QuestionSends> allInboxQuestionList = [];
  List<QuestionSends> allOutboxQuestionList = [];
  List<Tags> allTags = [];



  bool isRecipient = false;
  UserProfile? recipientProfile;
  UserRelationship? recipientRelationship;
  Notifications? selectedNotification;
  List<InboxMessage> allInboxMessage = [];

  List<UserProfile> allRequestsToYou = [];
  List<UserRelationship> allRequestsToYouRelationship = [];

  List<UserProfile> allApprovedByMe = [];
  List<UserRelationship> allApprovedByMeRelationship = [];

  List<UserProfile> allYourPeople = [];
  List<UserRelationship> allYourPeopleRelationship = [];

  List<UserProfile> allApprovedByOther = [];
  List<UserRelationship> allApprovedByOtherRelationship = [];

  List<UserProfile> allRequestsToYouRecover = [];
  List<UserRelationship> allRequestsToYouRelationshipRecover = [];

  List<UserProfile> allOtherProfilePic = [];

  List<Relation> allRecoverConnection = [];
  int recoverConnectIndexToRemove = -1;

  bool isPeopleTabReload = false;
  bool isUserDataForceLoad = false;

  bool isRecordStory = false;
  bool isConnectOne = false;
  bool isSendQuestion = false;

  AppsflyerSdk? appsflyerSdk;
  late CourierInboxListener notificatinListener;
  late CourierPushListener pushListener;
  bool isPushClicked = false;
  bool isPushClickedByUser = false;

  static final VideoTC _singleton = VideoTC._internal();

  factory VideoTC() {
    return _singleton;
  }

  VideoTC._internal();

  @override
  Future<void> onCreate() async {
    isPushClicked = false;
    isPushClickedByUser = false;
    setAppBasicUrls();
    await loadAppsFlyer();
    initEventBus();

    checkIfAlreadyHavePermission();
    FlutterContacts.config.includeNotesOnIos13AndAbove = false;

    initAmplitude();
    await Utility.instance.initPlatformState();

    accountBloc = AccountBloc();
    questionBloc = QuestionBloc();
  }

  @override
  void onTerminate() {
    eventBus!.destroy();
  }

  void initEventBus() {
    eventBus = EventBus();
  }

  checkIfAlreadyHavePermission() async {
    var status = await Permission.contacts.status;
    if (status.isGranted) {
      contactPermission = true;
    } else {
      contactPermission = false;
    }
    debugPrint("contactPermission $contactPermission");
    // contactPermission = await SharedPrefUtil.getBoolean(kPrefContactPermission);
  }

  Future<void> saveAccesToken(String strAccessToken) async {
    await SharedPrefUtil.writeString(kPrefAccessToken, strAccessToken);
  }

  Future<String> getAccesToken() async {
    return await SharedPrefUtil.getString(kPrefAccessToken);
  }

  Future<void> saveUserId(int id, String phoneNumber, String password) async {
    await SharedPrefUtil.writeInt(kPrefCurrentUserId, id);
    await SharedPrefUtil.writeString(kPrefPhoneNumber, phoneNumber);
    await SharedPrefUtil.writeString(kPrefPassword, password);
  }

  Future<int> getUserId() async {
    return await SharedPrefUtil.getInt(kPrefCurrentUserId);
  }

  Future<String> getPhoneNumber() async {
    return await SharedPrefUtil.getString(kPrefPhoneNumber);
  }

  Future<String> getPassword() async {
    return await SharedPrefUtil.getString(kPrefPassword);
  }

  // API থেকে প্রাপ্ত ব্যবহারকারীর ডেটা সিঙ্গেলটন অবজেক্টে সেট করা
  Future<void> setCurrentUser(Map<String, dynamic> userData, String phoneNumber,
      String password) async {
    // API রেসপন্স থেকে User অবজেক্ট তৈরি করা
    User currentUser = User.fromMap(userData);
    int userId = currentUser.id!;
    // userId = await getUserId();
    
    // ব্যবহারকারীর আইডি, ফোন নম্বর এবং পাসওয়ার্ড দিয়ে ব্যবহারকারীর তথ্য সেট করা
    await setCurrentUserFromUserId(userId, phoneNumber, password);
  }

  // ব্যবহারকারীর আইডি দিয়ে সমস্ত তথ্য সেট করা
  Future<void> setCurrentUserFromUserId(
      int userId, String phoneNumber, String password) async {
    // SharedPreferences এ ব্যবহারকারীর আইডি, ফোন নম্বর এবং পাসওয়ার্ড সংরক্ষণ করা
    await VideoTC().saveUserId(userId, phoneNumber, password);
    
    // API কল করে ব্যবহারকারীর প্রোফাইল তথ্য লোড করে সিঙ্গেলটন অবজেক্টে সেট করা
    currentUserProfile = await accountBloc!.getUserProfile(userId);
  }


  Future<void> logout() async {
    await saveAccesToken(DefaultValue.kDefaultString);
    await saveUserId(DefaultValue.kDefaultInt, DefaultValue.kDefaultString,
        DefaultValue.kDefaultString);
    await NotificationTC.getInstance().signOut();
    // saveUserId(DefaultValue.kDefaultInt);
    currentUserProfile = null;

    allInboxQuestionList.clear();
    allOutboxQuestionList.clear();
    questionBloc!.questionInboxListStatus = LoadingStatus.notStarted;
    questionBloc!.questionOutboxListStatus = LoadingStatus.notStarted;
  }



/*************  ✨ Windsurf Command 🌟  *************/
  ///
  /// The [userProfile] parameter is the recipient's profile.
  ///
  /// The [userRelationship] parameter is the recipient's relationship status.
  ///
  /// The [isRecipient] parameter is set to true if the user selects a recipient.
  ///
  /// The method does not return any value.
/*******  e5929edf-731d-47c1-9539-4362449083f2  *******/

  setQuestionTabRepient(
      UserProfile? userProfile, UserRelationship? userRelationship) {
    isRecipient = true;
    recipientProfile = userProfile;
    recipientRelationship = userRelationship;
  }

  /// Loads the main route.
  ///
  /// If the user is not logged in, navigates to the welcome route.
  ///
  /// If [isLoading] is true, shows the loading indicator.
  ///
  /// Otherwise, loads all questions, starts the notification service, and
  /// navigates to the main route.
  Future<void> loadMainRoute(context, {bool isLoading = false}) async {
    if (currentUserProfile == null) {
      Navigator.of(context).pushNamedAndRemoveUntil(
          welcomeRoute, (Route<dynamic> route) => false);
      return;
    }

    if (isLoading) {
      innerAuthLoader(context);
    }

    await startNotiFication();

    if (VideoTC().allTags.isEmpty) {
      await questionBloc!.getAllQuestionsTag();

      Tags tags = Tags.createTags(0, "Uncategorized", null);
      VideoTC().allTags.add(tags);
    }

    allQuestionListStatus = LoadingStatus.notStarted;
    await questionBloc!.getAllQuestionsList();
    setQuestionTagsCount();
    await questionBloc!.getInboxQuestionsList();
    // await UserProfileBloc().getRecoverConnectionList();
    await setTutorial();

    Future.delayed(const Duration(seconds: 1), () {
      Navigator.of(context)
          .pushNamedAndRemoveUntil(mainRoute, (Route<dynamic> route) => false);
    });
  }

  

  /// Sets the count of questions for each tag in the allTags list.
  ///
  /// Iterates over the allTags list and sets the questionCount property of each tag
  /// to the count of questions in the allQuestionList that have a tag with the same
  /// id as the current tag.
  ///
  /// Used to display the count of questions for each tag in the question tab.
  
  setQuestionTagsCount() {
    for (int i = 0; i < VideoTC().allTags.length; i++) {
      Tags tags = VideoTC().allTags[i];
      tags.questionCount = getTagQuestionCount(tags.id!, allQuestionList);
      // debugPrint("tags.questionCount ${tags.title}  ${tags.questionCount}");
    }
  }

  /// Returns the count of questions that have a tag with the given id.
  ///
  /// Iterates over the allQuestionList and checks if each question has a tag with
  /// the given id. If the question has no creator, it is only counted if
  /// the tag is in the list of tags for the question. If the question has a
  /// creator, it is always counted.
  ///
  /// Used by setQuestionTagsCount to set the questionCount property of each
  /// tag in the allTags list.
  int getTagQuestionCount(int tag, List<Questions> allQuestionList) {
    int tagCount = 0;
    for (int i = 0; i < allQuestionList.length; i++) {
      Questions questions = allQuestionList[i];
      if (questions.createdBy == null) {
        for (int j = 0; j < questions.tags!.length; j++) {
          Tags tagFromList = questions.tags![j];

          if (tagFromList.id == tag) {
            tagCount++;
            break;
          }
        }
      } else {
        if (tag == 0) {
          tagCount++;
        }
      }
    }
    return tagCount;
  }

  setUserRelationShip() async {
    allRequestsToYou = [];
    allRequestsToYouRelationship = [];

    allYourPeople = [];
    allYourPeopleRelationship = [];

    allApprovedByMe = [];
    allApprovedByMeRelationship = [];

    allApprovedByOther = [];
    allApprovedByOtherRelationship = [];
    if (currentUserProfile != null) {
      for (int i = 0;
          i < currentUserProfile!.toUserRelationshipsDetails!.length;
          i++) {
        setUserProfile(
            currentUserProfile!.toUserRelationshipsDetails![i], true);
      }

      for (int i = 0;
          i < currentUserProfile!.fromUserRelationshipsDetails!.length;
          i++) {
        setUserProfile(
            currentUserProfile!.fromUserRelationshipsDetails![i], false);
      }
    }

    /*for (int i = 0; i < allYourPeople.length; i++) {
      UserProfile yourPeople = allYourPeople[i];
      UserRelationship yourPeopleRelation = allYourPeopleRelationship[i];
      debugPrint(
          "yourPeople ${yourPeople.getFullName()} yourPeopleRelation ${yourPeopleRelation.connection_request_status}");
    }*/
    for (int i = 0; i < allQuestionList.length; i++) {
      Questions questions = allQuestionList[i];
      if (questions.to_user_id != null) {
        questions.sent_to_user =
            VideoTC().getReleationUserProfile(questions.to_user_id!);
        if (questions.sent_to_user!.id == null) {
          questions.sent_to_user =
              await UserProfileBloc().getUserProfileData(questions.to_user_id!);
        }
      }
    }
  }

  UserProfile setUserProfile(UserRelationship userRelationship, bool isToUser) {
    int userType = 0;
    if (isToUser == false) {
      if (userRelationship.connection_request_status ==
              ConnectionStatus.pending.value ||
          userRelationship.connection_request_status ==
              ConnectionStatus.resent.value) {
        userType = 1;
      } else if (userRelationship.connection_request_status ==
          ConnectionStatus.invited.value) {
        userType = 2;
      }
    } else {
      if (userRelationship.connection_request_status ==
          ConnectionStatus.approved.value) {
        userType = 3;
      }
    }

    UserProfile userProfileInfo = UserProfileBloc().createUserProfile(
      userRelationship.id!,
      userRelationship.first_name!,
      userRelationship.middle_name!,
      userRelationship.last_name!,
      phone_number: userRelationship.to_phone_number,
      storyCount: userRelationship.stories,
      familyCount: userRelationship.total_family_count,
      profile_pic: userRelationship.profile_pic,
      created_at: userRelationship.created_at,
      updated_at: userRelationship.updated_at,
      userType: userType,
      invitation_id: userRelationship.invitation_id,
    );

    if (userRelationship.connection_request_status ==
        ConnectionStatus.approved.value) {
      addInAllYourPeople(userProfileInfo, userRelationship);
      if (isToUser) {
        allApprovedByMe.add(userProfileInfo);
        allApprovedByMeRelationship.add(userRelationship);
      } else {
        allApprovedByOther.add(userProfileInfo);
        allApprovedByOtherRelationship.add(userRelationship);
      }
    } else if (userRelationship.connection_request_status ==
        ConnectionStatus.pending.value) {
      if (isToUser) {
        allRequestsToYou.add(userProfileInfo);
        allRequestsToYouRelationship.add(userRelationship);
      } else {
        addInAllYourPeople(userProfileInfo, userRelationship);
      }
    } else if (userRelationship.connection_request_status ==
        ConnectionStatus.resent.value) {
      if (isToUser) {
        allRequestsToYou.add(userProfileInfo);
        allRequestsToYouRelationship.add(userRelationship);
      } else {
        addInAllYourPeople(userProfileInfo, userRelationship);
      }
    } else if (userRelationship.connection_request_status ==
        ConnectionStatus.invited.value) {
      addInAllYourPeople(userProfileInfo, userRelationship);
    }
    return userProfileInfo;
  }

  addInAllYourPeople(userProfile, userRelationship) {
    int insertIndex = 0;
    while (insertIndex < allYourPeople.length) {
      UserRelationship yourPeopleRelation =
          allYourPeopleRelationship[insertIndex];
      if (yourPeopleRelation.connection_request_status !=
          ConnectionStatus.approved.value) {
        break;
      }
      insertIndex++;
    }

    if (userRelationship.connection_request_status ==
            ConnectionStatus.pending.value ||
        userRelationship.connection_request_status ==
            ConnectionStatus.resent.value ||
        userRelationship.connection_request_status ==
            ConnectionStatus.invited.value) {
      while (insertIndex < allYourPeople.length) {
        UserRelationship yourPeopleRelation =
            allYourPeopleRelationship[insertIndex];
        if (yourPeopleRelation.connection_request_status !=
            ConnectionStatus.pending.value) {
          break;
        }
        insertIndex++;
      }
    }

    if (userRelationship.connection_request_status ==
            ConnectionStatus.resent.value ||
        userRelationship.connection_request_status ==
            ConnectionStatus.invited.value) {
      while (insertIndex < allYourPeople.length) {
        UserRelationship yourPeopleRelation =
            allYourPeopleRelationship[insertIndex];
        if (yourPeopleRelation.connection_request_status !=
            ConnectionStatus.resent.value) {
          break;
        }
        insertIndex++;
      }
    }

    if (userRelationship.connection_request_status ==
        ConnectionStatus.invited.value) {
      while (insertIndex < allYourPeople.length) {
        UserRelationship yourPeopleRelation =
            allYourPeopleRelationship[insertIndex];
        if (yourPeopleRelation.connection_request_status !=
            ConnectionStatus.invited.value) {
          break;
        }
        insertIndex++;
      }
    }
    allYourPeople.insert(insertIndex, userProfile);
    allYourPeopleRelationship.insert(insertIndex, userRelationship);
  }

  bool isMyFriend(UserProfile userProfile) {
    for (int i = 0;
        i < currentUserProfile!.toUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile!.toUserRelationshipsDetails![i];
      if (userProfile.id == userRelationship.id) {
        if (userRelationship.connection_request_status ==
            ConnectionStatus.approved.value) {
          return true;
        } else {
          return false;
        }
      }
    }

    for (int i = 0;
        i < currentUserProfile!.fromUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile!.fromUserRelationshipsDetails![i];

      if (userProfile.id == userRelationship.id) {
        if (userRelationship.connection_request_status ==
            ConnectionStatus.approved.value) {
          return true;
        } else {
          return false;
        }
      }
    }
    return false;
  }

  bool isRequestSentToMe(UserProfile userProfile) {
    for (int i = 0;
        i < currentUserProfile!.toUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile!.toUserRelationshipsDetails![i];
      if (userProfile.id == userRelationship.id) {
        return true;
      }
    }
    return false;
  }

  bool isRequestSentFromMeAccepted(UserProfile userProfile) {
    for (int i = 0;
        i < currentUserProfile!.fromUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile!.fromUserRelationshipsDetails![i];
      if (userProfile.id == userRelationship.id &&
          userRelationship.connection_request_status !=
              ConnectionStatus.approved.value) {
        return false;
      }
    }
    return true;
  }

  UserRelationship getUserRelationWithSelf(int userId) {
    UserProfile currentUserProfile = VideoTC().currentUserProfile!;

    for (int i = 0;
        i < currentUserProfile.toUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile.toUserRelationshipsDetails![i];

      if (userRelationship.id == userId) {
        return userRelationship;
      }
    }

    for (int i = 0;
        i < currentUserProfile.fromUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile.fromUserRelationshipsDetails![i];
      if (userRelationship.id == userId) {
        return userRelationship;
      }
    }
    return UserRelationship();
  }

  discardUserRelationWithSelf(UserRelationship userRelationshipToRemove) {
    UserProfile currentUserProfile = VideoTC().currentUserProfile!;

    for (int i = 0;
        i < currentUserProfile.toUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile.toUserRelationshipsDetails![i];
      if (userRelationship.relationship_id ==
          userRelationshipToRemove.relationship_id) {
        currentUserProfile.toUserRelationshipsDetails!.removeAt(i);
        break;
      }
    }

    for (int i = 0;
        i < currentUserProfile.fromUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile.fromUserRelationshipsDetails![i];
      if (userRelationship.relationship_id ==
          userRelationshipToRemove.relationship_id) {
        currentUserProfile.fromUserRelationshipsDetails!.removeAt(i);
        break;
      }
    }
  }

  connectionStatusDone(
      ConnectionStatus connectionStatus,
      UserProfile userProfile,
      UserRelationship userRelationship,
      Relationships relationships,
      {bool isCurrentUserDone = false}) {
    debugPrint("VideoTC() connectionStatusDone $connectionStatus");
    switch (connectionStatus) {
      case ConnectionStatus.pending:
        userProfile.userType = 1;
        userRelationship.created_at = DateTime.now().toString();
        userRelationship.updated_at = DateTime.now().toString();
        removeUserProfileFromList(userProfile);
        addInAllYourPeople(userProfile, userRelationship);
        if (isCurrentUserDone == false) {
          VideoTC()
              .currentUserProfile!
              .fromUserRelationshipsDetails!
              .add(userRelationship);
        }
        break;
      case ConnectionStatus.approved:
        {
          UserRelationship userRelationshipSelf =
              getUserRelationWithSelf(userProfile.id!);
          userRelationship.updated_at = DateTime.now().toString();
          userRelationshipSelf.connection_request_status =
              ConnectionStatus.approved.value;
          removeUserProfileFromList(userProfile,
              userRelationshipToRemove: userRelationship);
          addInAllYourPeople(userProfile, userRelationship);

          allApprovedByMe.add(userProfile);
          allApprovedByMeRelationship.add(userRelationship);
        }
        break;
      case ConnectionStatus.invited:
        break;
      case ConnectionStatus.discarded:
        {
          debugPrint("VideoTC() ConnectionStatus.discarded");
          bool isFromUser = false;
          for (var i = 0; i < allRequestsToYou.length; i++) {
            UserProfile userProfile = allRequestsToYou[i];
            if (userProfile.id == userRelationship.id) {
              isFromUser = true;
              break;
            }
          }
          if (isFromUser) {
            // Relation relation = UserProfileBloc().createRelaionForFromUser(
            //     relationships.relationship_id!, userProfile, userRelationship);
            Relation relation = UserProfileBloc()
                .createRelaionForFromUser(0, userProfile, userRelationship);
            VideoTC().allRecoverConnection.add(relation);

            for (int i = 0; i < VideoTC().allInboxQuestionList.length; i++) {
              QuestionSends questionSends = VideoTC().allInboxQuestionList[i];

              if (questionSends.sent_from_user_id == userProfile.id) {
                VideoTC().allInboxQuestionList.removeAt(i);
                i--;
              }
            }
          }
          for (int i = 0; i < VideoTC().allOutboxQuestionList.length; i++) {
            QuestionSends questionSends = VideoTC().allOutboxQuestionList[i];

            if (questionSends.sent_to_user_id == userProfile.id) {
              VideoTC().allOutboxQuestionList.removeAt(i);
              i--;
            }
          }

          removeUserProfileFromList(userProfile,
              userRelationshipToRemove: userRelationship);
          currentUserProfile!.total_family_count =
              currentUserProfile!.total_family_count! - 1;
          debugPrint("isCurrentUserDone $isCurrentUserDone");
          if (isCurrentUserDone == false) {
            debugPrint("isCurrentUserDone ${userProfile.id!}}");
            VideoTC().discardUserRelationWithSelf(userRelationship);
          }
        }
        break;
      case ConnectionStatus.resent:
        userRelationship.updated_at = DateTime.now().toString();
        debugPrint("VideoTC() ConnectionStatus.resent");
        break;
      case ConnectionStatus.recover:
        if (recoverConnectIndexToRemove != -1) {
          VideoTC().allRecoverConnection.removeAt(recoverConnectIndexToRemove);
          recoverConnectIndexToRemove = -1;
        }
        VideoTC().allRequestsToYou.add(userProfile);
        VideoTC().allRequestsToYouRelationship.add(userRelationship);
        break;
    }
  }

  removeUserProfileFromList(UserProfile userProfileToRemove,
      {UserRelationship? userRelationshipToRemove}) {
    if (userRelationshipToRemove != null &&
        userRelationshipToRemove!.invitation_id != 0 &&
        userRelationshipToRemove!.id == 0) {
      for (var i = 0; i < allRequestsToYou.length; i++) {
        UserRelationship userRelationship = allRequestsToYouRelationship[i];
        if (userRelationship.invitation_id ==
            userRelationshipToRemove.invitation_id) {
          allRequestsToYou.removeAt(i);
          allRequestsToYouRelationship.removeAt(i);
          break;
        }
      }
      for (var i = 0; i < allYourPeople.length; i++) {
        UserRelationship userRelationship = allYourPeopleRelationship[i];
        if (userRelationship.invitation_id ==
            userProfileToRemove.invitation_id) {
          allYourPeople.removeAt(i);
          allYourPeopleRelationship.removeAt(i);

          break;
        }
      }
    } else {
      for (var i = 0; i < allRequestsToYou.length; i++) {
        UserProfile userProfile = allRequestsToYou[i];
        if (userProfile.id == userProfileToRemove.id) {
          allRequestsToYou.removeAt(i);
          allRequestsToYouRelationship.removeAt(i);
          break;
        }
      }
      for (var i = 0; i < allYourPeople.length; i++) {
        UserProfile userProfile = allYourPeople[i];
        if (userProfile.id == userProfileToRemove.id) {
          allYourPeople.removeAt(i);
          allYourPeopleRelationship.removeAt(i);

          break;
        }
      }
    }
  }

  UserProfile getReleationUserProfile(int userId) {
    if (userId == currentUserProfile!.id) {
      return currentUserProfile!;
    }
    for (int i = 0;
        i < currentUserProfile!.toUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile!.toUserRelationshipsDetails![i];
      if (userRelationship.id == userId) {
        return getUserProfile(userRelationship, true);
      }
    }
    if (isRecipient == false) {
      for (int i = 0;
          i < currentUserProfile!.fromUserRelationshipsDetails!.length;
          i++) {
        UserRelationship userRelationship =
            currentUserProfile!.fromUserRelationshipsDetails![i];
        if (userRelationship.id == userId) {
          return getUserProfile(userRelationship, true);
        }
      }
    }
    return UserProfile();
  }

  UserProfile getUserProfile(UserRelationship userRelationship, bool isToUser) {
    int userType = 0;
    if (isToUser == false) {
      if (userRelationship.connection_request_status ==
          ConnectionStatus.pending.value) {
        userType = 1;
      } else if (userRelationship.connection_request_status ==
          ConnectionStatus.invited.value) {
        userType = 2;
      }
    } else {
      if (userRelationship.connection_request_status ==
          ConnectionStatus.approved.value) {
        userType = 3;
      }
    }

    return UserProfileBloc().createUserProfile(
        userRelationship.id!,
        userRelationship.first_name!,
        userRelationship.middle_name!,
        userRelationship.last_name!,
        storyCount: userRelationship.stories,
        familyCount: userRelationship.total_family_count,
        profile_pic: userRelationship.profile_pic,
        created_at: userRelationship.created_at,
        updated_at: userRelationship.updated_at,
        userType: userType);
  }

  UserProfile? getUserProfileForUser(int userId) {
    for (int i = 0;
        i < currentUserProfile!.toUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile!.toUserRelationshipsDetails![i];
      if (userRelationship.id == userId) {
        return UserProfileBloc().createUserProfile(
            userRelationship.id!,
            userRelationship.first_name!,
            userRelationship.middle_name!,
            userRelationship.last_name!,
            storyCount: userRelationship.stories,
            familyCount: userRelationship.total_family_count,
            profile_pic: userRelationship.profile_pic,
            created_at: userRelationship.created_at,
            updated_at: userRelationship.updated_at,
            userType: 0);
      }
    }
    for (int i = 0;
        i < currentUserProfile!.fromUserRelationshipsDetails!.length;
        i++) {
      UserRelationship userRelationship =
          currentUserProfile!.fromUserRelationshipsDetails![i];
      if (userRelationship.id == userId) {
        return UserProfileBloc().createUserProfile(
          userRelationship.id!,
          userRelationship.first_name!,
          userRelationship.middle_name!,
          userRelationship.last_name!,
          storyCount: userRelationship.stories,
          familyCount: userRelationship.total_family_count,
          profile_pic: userRelationship.profile_pic,
          created_at: userRelationship.created_at,
          updated_at: userRelationship.updated_at,
          userType: 0,
          invitation_id: userRelationship.invitation_id,
        );
      }
    }
    return null;
  }

  Future<void> loadAppsFlyer() async {
    AppsFlyerOptions appsFlyerOptions = AppsFlyerOptions(
      afDevKey: "LmtMY426vAwKeSHGw4V9EY",
      appId: "1554817576",
      showDebug: true,
      timeToWaitForATTUserAuthorization: 15, // for iOS 14.5
      //appInviteOneLink: "https://hq1.appsflyer.com/onelink", // Optional field
      // disableAdvertisingIdentifier: false, // Optional field
      // disableCollectASA: false, //Optional field
      manualStart: false, // Changed to false to start immediately
    ); // Optional field

    appsflyerSdk = AppsflyerSdk(appsFlyerOptions);

    await appsflyerSdk!.initSdk(
        registerConversionDataCallback: true,
        registerOnAppOpenAttributionCallback: true,
        registerOnDeepLinkingCallback: true);

    debugPrint("appsflyerSdk initSdk successfully");

    // Conversion data callback
    appsflyerSdk!.onInstallConversionData((res) {
      debugPrint("onInstallConversionData res: $res");
    });

    // App open attribution callback
    appsflyerSdk!.onAppOpenAttribution((res) {
      debugPrint("onAppOpenAttribution res: $res");
    });

    // Deep linking callback
    appsflyerSdk!.onDeepLinking((DeepLinkResult dp) {
      switch (dp.status) {
        case Status.FOUND:
          debugPrint(dp.deepLink?.toString());
          debugPrint("deep link value: ${dp.deepLink?.deepLinkValue}");
          break;
        case Status.NOT_FOUND:
          debugPrint("deep link not found");
          break;
        case Status.ERROR:
          debugPrint("deep link error: ${dp.error}");
          break;
        case Status.PARSE_ERROR:
          debugPrint("deep link status parsing error");
          break;
      }
      debugPrint("onDeepLinking res: $dp");
    });

    //_appsflyerSdk.anonymizeUser(true);
    if (Platform.isAndroid) {
      appsflyerSdk!.performOnDeepLinking();
    }
  }

  setLogEvent(LogEvent logEvent, {bool hasAccount = false, int numAttempts = 0, bool isUploaded = false}) async {
    if (currentUserProfile == null && logEvent != LogEvent.enterPhoneNumber) {
      return;
    }
    String eventName = "af_login";
    switch (logEvent) {
      case LogEvent.login:
        eventName = "af_login";
        break;
      case LogEvent.register:
        eventName = "af_complete_registration";
        break;
      case LogEvent.enterPhoneNumber:
        eventName = "enter_phone_number";
        break;
      case LogEvent.verifyPhoneNumber:
        eventName = "verify_phone_number";
        break;
      case LogEvent.enterNameAndAcceptTerms:
        eventName = "enter_name_and_accept_terms";
        break;
      case LogEvent.createPassword:
        eventName = "create_password";
        break;
      case LogEvent.passProfilePicScreen:
        eventName = "pass_profile_pic_screen";
        break;
    }

    Map<String, String>? eventValue;
    switch (logEvent) {
      case LogEvent.login:
      case LogEvent.register:
        {
          eventValue = {
            "user_id": VideoTC().currentUserProfile!.id.toString(),
            "user_name": VideoTC().currentUserProfile!.getFullName(),
            "phone_number":
                VideoTC().currentUserProfile!.phone_number.toString()
          };
        }
        break;
      case LogEvent.enterPhoneNumber:
        {
          eventValue = {
            "has_account": hasAccount.toString(),
          };
        }
        break;
      case LogEvent.verifyPhoneNumber:
      case LogEvent.enterNameAndAcceptTerms:
      case LogEvent.createPassword:
        {
          eventValue = {
            "num_attempts": numAttempts.toString(),
          };
        }
        break;
      case LogEvent.passProfilePicScreen:
        {
          eventValue = {
            "is_uploaded": isUploaded.toString(),
          };
        }
        break;
    }

    await this.logEvent(eventName, eventValue);
  }

  logEvent(String eventName, Map? eventValues) async {
    bool? result;
    try {
      result = await appsflyerSdk!.logEvent(eventName, eventValues);
      debugPrint("Result logEvent: $eventName $eventValues $result");
    } on Exception catch (e) {
      debugPrint("Result logEvent: $eventName $e");
    }
  }

  setTutorial() async {
    isRecordStory = await isRecordStoryDone();
    isConnectOne = await isConnectOneDone();
    isSendQuestion = await isSendQuestionDone();
    if (isRecordStory == false) {
      if (currentUserProfile!.stories! > 0) {
        await updateRecordStory();
        isRecordStory = true;
      }
    }
    if (isConnectOne == false) {
      if (currentUserProfile!.total_family_count! > 0) {
        await updateConnectOne();
        isConnectOne = true;
      }
    }
    if (isSendQuestion == false) {
      if (currentUserProfile!.question_sends_count! > 0) {
        await updateSendQuestion();
        isSendQuestion = true;
      }
    }
  }

  addInAlOtherProfile(UserProfile userProfile) {
    UserProfile userProfileWithMe = getReleationUserProfile(userProfile.id!);

    if (userProfileWithMe.id == null) {
      bool isAlreadyAdded = false;
      for (int i = 0; i < allOtherProfilePic.length; i++) {
        if (allOtherProfilePic[i].id == userProfile.id) {
          isAlreadyAdded = true;
          break;
        }
      }
      if (isAlreadyAdded == false) {
        allOtherProfilePic.add(userProfile);
      }
    }
  }

  startNotiFication() async {
    // debugPrint("startNotiFication--------");

    /* NotificationPermissionStatus currentPermissionStatus =
        await Courier.shared.getNotificationPermissionStatus();
    debugPrint("currentPermissionStatus------ $currentPermissionStatus");
    if (currentPermissionStatus != NotificationPermissionStatus.authorized) {
      currentPermissionStatus =
          await Courier.shared.requestNotificationPermission();
      debugPrint("currentPermissionStatus------1 $currentPermissionStatus");
    }*/
    NotificationSettings currentPermissionStatus = await initFirebaseMessage();
    // debugPrint(
    //     "currentPermissionStatus ${currentPermissionStatus.authorizationStatus}");
    if (currentPermissionStatus.authorizationStatus ==
        AuthorizationStatus.authorized) {
      await confiqureNotification();
    }
  }

  confiqureNotification() async {
    /*NotificationPermissionStatus currentPermissionStatus =
        await Courier.shared.getNotificationPermissionStatus();
    debugPrint(
        "confiqureNotification currentPermissionStatus $currentPermissionStatus");*/
    await NotificationTC.getInstance().start();
    await NotificationTC.getInstance().finishCongiqure();

    // showAPIDialog(context,
    //     "currentPermissionStatus $currentPermissionStatus authKey ${NotificationTC().authKey} clintKey ${NotificationTC().clintKey}");

    // await setupInteractedMessage(context);

    // debugPrint("confiqureNotification ${Courier.shared.userId}");
    // final currentUserId = await Courier.shared.userId;
    // showAPIDialog(context, "confiqureNotification $currentUserId");

    //var data = await Courier.shared.readAllInboxMessages();
    // debugPrint("startNotiFication data $data");
    /*List<InboxMessage> fetchMessage =
              await Courier.shared.fetchNextPageOfMessages();
          debugPrint("fetchMessage ${fetchMessage.length}");*/
    /*
            for (int i = 0; i < fetchMessage.length; i++) {
              InboxMessage inboxMessage = fetchMessage[i];
              debugPrint("__________________");
              debugPrint("fetchMessage index $i");
              debugPrint("inboxMessage.messageId ${inboxMessage.messageId}");
              debugPrint("inboxMessage.title ${inboxMessage.title}");
              if (inboxMessage.body != null) {
                //debugPrint("inboxMessage.body ${inboxMessage.body!}");
              }
              // debugPrint("inboxMessage ${inboxMessage.preview}");
            }
          }*/

    notificatinListener = await Courier.shared.addInboxListener(
      onInitialLoad: () {
        //debugPrint("onInitialLoad");
        // showAPIDialog(context, "onInitialLoad");
      },
      onError: (error) {
        //debugPrint("notificatinListener error $error");
        //showAPIDialog(context, "notificatinListener error $error");
      },
      onMessagesChanged:
          (messages, unreadMessageCount, totalMessageCount, canPaginate) {
        debugPrint("canPaginate $canPaginate");
        // showAPIDialog(context, "canPaginate $canPaginate");
        debugPrint("onMessagesChanged messages ${messages.length}");

        // showAPIDialog(
        //     context, "onMessagesChanged messages ${messages.length}");
        VideoTC().allInboxMessage = messages;

        debugPrint("totalMessageCount $totalMessageCount");

        // showAPIDialog(context, "totalMessageCount $totalMessageCount");
        debugPrint("notificationCount $unreadMessageCount");
        // showAPIDialog(context, "notificationCount $unreadMessageCount");
        // debugPrint("Inbox Messages: ${fetchMessage.map((msg) => msg.toJson()).toList()}");
        // debugPrint(
        //     "onMessagesChanged raw messages: ${messages.map((msg) => msg.subtitle.toString()).toList()}");
        // messages.map((msg) => {
        //       debugPrint("__________________"),
        //       debugPrint("inboxMessage.messageId ${msg.messageId}"),
        //       debugPrint("inboxMessage.title ${msg.title}"),
        //       if (msg.body != null)
        //         {
        //           debugPrint("inboxMessage.body ${msg.body!}"),
        //         },
        //       debugPrint("inboxMessage subtitle ${msg.subtitle}"),
        //       debugPrint("inboxMessage preview ${msg.preview}"),
        //     });
        /*debugPrint("messages ${messages.length}");
              for (int i = messages.length - 1; i >= 0; i--) {
                InboxMessage inboxMessage = messages[i];
                debugPrint("__________________");
                debugPrint("inboxMessage.messageId ${inboxMessage.messageId}");
                debugPrint("inboxMessage.title ${inboxMessage.title}");
                // if (inboxMessage.body != null) {
                debugPrint("inboxMessage.body ${inboxMessage.body}");
                // }
                debugPrint("inboxMessage preview ${inboxMessage.preview}");
                debugPrint("inboxMessage subtitle ${inboxMessage.read}");
              }*/

        for (int i = messages.length - 1; i >= 0; i--) {
          InboxMessage inboxMessage = messages[i];

          // debugPrint("inboxMessage.title ${inboxMessage.title}");
          Map<String, dynamic> jsonData =
              json.decode(inboxMessage.preview.toString());
          Notifications notifications = Notifications.fromMap(jsonData);
          jsonData.containsKey("notificationData")
              ? notifications.loadNotificationData(jsonData["notificationData"])
              : "";
          // debugPrint("notifications.title ${notifications.title}");
          // debugPrint("notifications.eventType ${notifications.eventType}");
          if (notifications.eventType == "question_unlocked") {
            addUnlockQuestion(inboxMessage);
          } else if (notifications.eventType == "question_sent") {
            if (inboxMessage.isRead == false) {
              VideoTC().questionBloc!.questionInboxListStatus =
                  LoadingStatus.notStarted;
            }
          } else if (notifications.eventType == "question_answered") {
            if (inboxMessage.isRead == false) {
              VideoTC().questionBloc!.questionOutboxListStatus =
                  LoadingStatus.notStarted;
            }
          } else if (notifications.eventType == "question_sent") {
            if (inboxMessage.isRead == false) {
              VideoTC().questionBloc!.questionInboxListStatus =
                  LoadingStatus.notStarted;
            }
          } else if (notifications.eventType ==
                  "connection_request_receive_pending" ||
              notifications.eventType ==
                  "connection_request_receive_approved") {
            if (inboxMessage.isRead == false) {
              VideoTC().questionBloc!.isInboxUserDataLoad = true;
              VideoTC().isPeopleTabReload = true;
            }
          }
        }
        debugPrint("unreadMessageCount $unreadMessageCount");

        if (MainPageState.getInstance() != null) {
          MainPageState.getInstance()!
              .courierNotificationLister(unreadMessageCount);
        }
        if (isPushClickedByUser == true) {
          if (MainPageState.getInstance() == null) {
            isPushClicked = true;
          } else {
            isPushClicked = false;
            MainPageState.getInstance()!.loadNotificationPushClick();
          }
          isPushClickedByUser = false;
        }
      },
    );

    pushListener = await Courier.shared.addPushListener(
      onPushClicked: (messages) => {
        debugPrint("addPushListener pushListener $isPushClicked"),
        setDataForNotification(messages),
      },
      onPushDelivered: (inboxMessage) => {
        // debugPrint("onPushDelivered $inboxMessage"),
        //Courier.shared.refreshInbox(),
        // InboxMessage inboxMessage = messages;
        // debugPrint("inboxMessage.messageId ${inboxMessage.messageId}"),
        // debugPrint("inboxMessage.title ${inboxMessage.title}"),
        // debugPrint("inboxMessage.body ${inboxMessage.data["brandId"]}"),
        // debugPrint("inboxMessage ${inboxMessage.preview}"),
      },
    );
  }

  addUnlockQuestion(InboxMessage inboxMessage) {
    if (inboxMessage.preview != "[Error]") {
      Map<String, dynamic> jsonData =
          json.decode(inboxMessage.preview.toString());

      Notifications notifications = Notifications.fromMap(jsonData);
      jsonData.containsKey("notificationData")
          ? notifications.loadNotificationData(jsonData["notificationData"])
          : "";

      bool isNewQuestion = true;
      for (int i = 0; i < VideoTC().allQuestionList.length; i++) {
        Questions questions = VideoTC().allQuestionList[i];
        if (questions.id == notifications.question_id) {
          isNewQuestion = false;
          break;
        }
      }
      if (isNewQuestion) {
        Questions questions = Questions();
        questions.id = notifications.question_id;
        questions.question_text = notifications.question_text;
        questions.source_question_id = null;
        questions.creator__user_id = null;
        questions.createdBy = null;
        questions.questionActivities = [];
        questions.tags = [];
        int tagId = notifications.tag_id!;

        for (int i = 0; i < VideoTC().allTags.length; i++) {
          Tags tags = VideoTC().allTags[i];

          if (tags.id == tagId) {
            questions.tags!.add(VideoTC().allTags[i]);
          }
        }

        questions.created_at = DateTime.now().toString();
        questions.updated_at = DateTime.now().toString();
        questions.to_user_id = notifications.to_user_id;
        questions.sent_to_user =
            VideoTC().getReleationUserProfile(questions.to_user_id!);
        if (questions.sent_to_user!.id == null) {
          questions.sent_to_user = null;
        }

        VideoTC().allQuestionList.insert(0, questions);
      }
    }
  }

  setDataForNotification(messages) async {
    // MainPageState.getInstance()!.showAlertPushClick();
    isPushClickedByUser = true;
  }
}
