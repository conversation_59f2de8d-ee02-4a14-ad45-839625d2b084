import 'dart:async';
import 'dart:ui';

import 'package:in_app_review/in_app_review.dart';
import 'package:legacylock_app/helper/app_start/videotc_application.dart';
import 'package:legacylock_app/helper/util/shared_preference.dart';

const kPrefUserUsername = "user_username";

const kPrefContactPermission = "user_sub_id";

const kPrefAccessToken = "access_token";
const kPrefCurrentUserId = "current_user_id";
const kPrefPhoneNumber = "current_user_phone_number";
const kPrefPassword = "current_user_password";

const kAcceptNotification = "accept_notification";

enum ConnectionStatus { pending, approved, invited, discarded, resent, recover }

enum RelationShipState { otherRelationToMe, ownReationToOther, sendRequest }

enum FontType { lora, cabin, agrandir, lato }

enum LogEvent {
  login,
  register,
  enterPhoneNumber,
  verifyPhoneNumber,
  enterNameAndAcceptTerms,
  createPassword,
  passProfilePicScreen,
}

extension ConnectionStatusExtension on ConnectionStatus {
  String get value {
    switch (this) {
      case ConnectionStatus.pending:
        return "pending";
      case ConnectionStatus.approved:
        return "approved";
      case ConnectionStatus.invited:
        return "invited";
      case ConnectionStatus.discarded:
        return "discarded";
      case ConnectionStatus.resent:
        return "resent";
      case ConnectionStatus.recover:
        return "recover";
    }
  }
}

enum QustionActivityStatus {
  sent,
  received,
  answered,
  resent,
  custom,
  inbox,
  unlockQuestion,
  discarded,
}

extension QustionActivityStatusExtension on QustionActivityStatus {
  String get value {
    switch (this) {
      case QustionActivityStatus.sent:
        return "sent";
      case QustionActivityStatus.received:
        return "received";
      case QustionActivityStatus.answered:
        return "answered";
      case QustionActivityStatus.resent:
        return "resent";
      case QustionActivityStatus.custom:
        return "custom";
      case QustionActivityStatus.unlockQuestion:
        return "unlockQuestion";

      default:
        return "";
    }
  }
}

enum NotificationType {
  comment,
  view,
  like,
  sentMeQuestion,
  answeredYourQuestion,
  acceptedMyInvitation, //bad
  requestedToConnectWithMe,
  requestedToConnectWithSomeone,
  acceptedRequestByMe,
  acceptedRequestBySomeone,
  discardedRequestToConnect,
  unlockNewQuestion,
  invitationAccepted,
}

extension NotificationExtension on NotificationType {
  String get value {
    switch (this) {
      case NotificationType.comment:
        return "Comment";
      case NotificationType.view:
        return "View";
      case NotificationType.like:
        return "Like";
      case NotificationType.sentMeQuestion:
        return "sentmequestion";
      case NotificationType.answeredYourQuestion:
        return "answeredyourquestion";
      case NotificationType.acceptedMyInvitation:
        return "acceptedmyinvitation";
      case NotificationType.requestedToConnectWithMe:
        return "requestedtoconnectWithMe";
      case NotificationType.requestedToConnectWithSomeone:
        return "requestedToConnectWithSomeone";
      case NotificationType.acceptedRequestByMe:
        return "acceptedRequestByMe";
      case NotificationType.discardedRequestToConnect:
        return "discardedRequestToConnect";
      case NotificationType.unlockNewQuestion:
        return "unlockNewQuestion";
      case NotificationType.invitationAccepted:
        return "invitationAccepted";

      default:
        return "";
    }
  }
}

bool validateEmail(String email) {
  return RegExp(
          r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
      .hasMatch(email);
}

bool validatePhone(String phone) {
  return RegExp(
          r"^\s*(?:\+?(\d{1,3}))?[-. (]*(\d{3})[-. )]*(\d{3})[-. ]*(\d{4})(?: *x(\d+))?\s*$")
      .hasMatch(phone);
}

bool validateUsername(String username) {
  return RegExp(r"^[a-zA-Z][a-zA-Z0-9_.]{2,19}$").hasMatch(username);
}

class DefaultValue {
  static const bool kDefaultBoolean = false;
  static const int kDefaultInt = 0;
  static const double kDefaultDouble = 0.0;
  static const String kDefaultString = '';
}

class Debouncer {
  Debouncer({required this.milliseconds});

  final int milliseconds;
  Timer? timer;

  void run(VoidCallback action) {
    if (timer?.isActive ?? false) {
      timer?.cancel();
    }
    timer = Timer(Duration(milliseconds: milliseconds), action);
  }
}

const kPrefRecordStory = "record_story";
const kPrefConnectOne = "connect_one";
const kPrefSendQuestion = "send_question";

Future<bool> isRecordStoryDone() async {
  return await SharedPrefUtil.getBoolean(
      kPrefRecordStory + VideoTC().currentUserProfile!.id!.toString());
}

Future<void> updateRecordStory() async {
  await SharedPrefUtil.writeBoolean(
      kPrefRecordStory + VideoTC().currentUserProfile!.id!.toString(), true);
  await showRateMyApp();
}

Future<bool> isConnectOneDone() async {
  return await SharedPrefUtil.getBoolean(
      kPrefConnectOne + VideoTC().currentUserProfile!.id!.toString());
}

Future<void> updateConnectOne() async {
  await SharedPrefUtil.writeBoolean(
      kPrefConnectOne + VideoTC().currentUserProfile!.id!.toString(), true);
  await showRateMyApp();
}

Future<bool> isSendQuestionDone() async {
  return await SharedPrefUtil.getBoolean(
      kPrefSendQuestion + VideoTC().currentUserProfile!.id!.toString());
}

Future<void> updateSendQuestion() async {
  await SharedPrefUtil.writeBoolean(
      kPrefSendQuestion + VideoTC().currentUserProfile!.id!.toString(), true);
  await showRateMyApp();
}

final InAppReview inAppReview = InAppReview.instance;
const kRateShow = "rate_show";

showRateMyApp() async {
  bool isRateShow = await SharedPrefUtil.getBoolean(
      kRateShow + VideoTC().currentUserProfile!.id!.toString());

  if (isRateShow == false) {
    bool isRecordStory = VideoTC().isRecordStory;
    bool isConnectOne = VideoTC().isConnectOne;
    bool isSendQuestion = VideoTC().isSendQuestion;
    if (isRecordStory == true &&
        isConnectOne == true &&
        isSendQuestion == true) {
      if (await inAppReview.isAvailable()) {
        inAppReview.requestReview();

        SharedPrefUtil.writeBoolean(
            kRateShow + VideoTC().currentUserProfile!.id!.toString(), true);
      }
    }
  }
}
