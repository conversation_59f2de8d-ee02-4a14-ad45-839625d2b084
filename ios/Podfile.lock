PODS:
  - amplitude_flutter (0.0.1):
    - AmplitudeSwift (~> 1.11)
    - Flutter
    - FlutterMacOS
  - AmplitudeCore (1.0.10)
  - AmplitudeSwift (1.13.0):
    - AmplitudeCore (< 2.0.0, >= 1.0.10)
    - AnalyticsConnector (~> 1.3.0)
  - AnalyticsConnector (1.3.1)
  - appsflyer_sdk (6.16.2):
    - AppsFlyerFramework (= 6.16.2)
    - Flutter
  - AppsFlyerFramework (6.16.2):
    - AppsFlyerFramework/Main (= 6.16.2)
  - AppsFlyerFramework/Main (6.16.2)
  - Bugsnag (6.32.1)
  - bugsnag_flutter (0.0.1):
    - Bugsnag (= 6.32.1)
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - courier_flutter (0.0.1):
    - Courier_iOS (= 4.5.4)
    - Flutter
  - Courier_iOS (4.5.4)
  - device_info_plus (0.0.1):
    - Flutter
  - device_region (0.0.1):
    - Flutter
  - devicelocale (0.0.1):
    - Flutter
  - Firebase/Analytics (11.10.0):
    - Firebase/Core
  - Firebase/Core (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.10.0)
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - firebase_analytics (11.4.5):
    - Firebase/Analytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.0):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_messaging (15.2.5):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.10.0):
    - FirebaseAnalytics/AdIdSupport (= 11.10.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_contacts (0.0.1):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_libphonenumber_ios (1.1.4):
    - Flutter
    - PhoneNumberKit/PhoneNumberKitCore (= 3.8.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - GoogleAppMeasurement (11.10.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.10.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.10.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_review (2.0.0):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PhoneNumberKit/PhoneNumberKitCore (3.8.0)
  - PromisesObjC (2.4.0)
  - ReachabilitySwift (5.2.4)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock (0.0.1):
    - Flutter

DEPENDENCIES:
  - amplitude_flutter (from `.symlinks/plugins/amplitude_flutter/darwin`)
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - bugsnag_flutter (from `.symlinks/plugins/bugsnag_flutter/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - courier_flutter (from `.symlinks/plugins/courier_flutter/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - device_region (from `.symlinks/plugins/device_region/ios`)
  - devicelocale (from `.symlinks/plugins/devicelocale/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_contacts (from `.symlinks/plugins/flutter_contacts/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_libphonenumber_ios (from `.symlinks/plugins/flutter_libphonenumber_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - gal (from `.symlinks/plugins/gal/darwin`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)

SPEC REPOS:
  trunk:
    - AmplitudeCore
    - AmplitudeSwift
    - AnalyticsConnector
    - AppsFlyerFramework
    - Bugsnag
    - Courier_iOS
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PhoneNumberKit
    - PromisesObjC
    - ReachabilitySwift
    - TOCropViewController

EXTERNAL SOURCES:
  amplitude_flutter:
    :path: ".symlinks/plugins/amplitude_flutter/darwin"
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  bugsnag_flutter:
    :path: ".symlinks/plugins/bugsnag_flutter/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  courier_flutter:
    :path: ".symlinks/plugins/courier_flutter/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  device_region:
    :path: ".symlinks/plugins/device_region/ios"
  devicelocale:
    :path: ".symlinks/plugins/devicelocale/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_contacts:
    :path: ".symlinks/plugins/flutter_contacts/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_libphonenumber_ios:
    :path: ".symlinks/plugins/flutter_libphonenumber_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  gal:
    :path: ".symlinks/plugins/gal/darwin"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"

SPEC CHECKSUMS:
  amplitude_flutter: fd9bf76a1885fe760055877777da18ca4d087088
  AmplitudeCore: 2d2171ab1af886c8cd0cef2814ea75aef1553e51
  AmplitudeSwift: 46a285740b7d586895ccf24f19db8ca391ae4e71
  AnalyticsConnector: 3def11199b4ddcad7202c778bde982ec5da0ebb3
  appsflyer_sdk: b8bf0ef73cadbebbad020a719ebc9bb42fe20526
  AppsFlyerFramework: fe5303bffcdfd941d5f570c2d21eaaea982e7bdc
  Bugsnag: 265186a1ec7af706c3a96ab628e4f360d2fc9ce2
  bugsnag_flutter: b9b09d1e604a04fe9837cafc17f435f2376e36c9
  camera_avfoundation: adb0207d868b2d873e895371d88448399ab78d87
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  courier_flutter: efaa87a6ccb93cd83327e4ceccc356102c207bf1
  Courier_iOS: 57d77d0b6a07710ca4798aea559f9451aafb25c5
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  device_region: 896a80820f38a841066c458ac96ad821c1e259d3
  devicelocale: 35ba84dc7f45f527c3001535d8c8d104edd5d926
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_analytics: 4b8609ce8d2e0c8928472bec8d9753a8f1835eb6
  firebase_core: 432718558359a8c08762151b5f49bb0f093eb6e0
  firebase_messaging: 3b99522baf7480dfb4b7683d2b34e842d577c362
  FirebaseAnalytics: 4e42333f02cf78ed93703a5c36f36dd518aebdef
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_contacts: edb1c5ce76aa433e20e6cb14c615f4c0b66e0983
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_libphonenumber_ios: 5ad8620197bff91dd159d828b7544ec6956e86a1
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  gal: 6a522c75909f1244732d4596d11d6a2f86ff37a5
  GoogleAppMeasurement: 36684bfb3ee034e2b42b4321eb19da3a1b81e65d
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  image_cropper: e0bb0042e4404ff2ef134e5cf0492cbd892156cd
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  in_app_review: a31b5257259646ea78e0e35fc914979b0031d011
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PhoneNumberKit: ec00ab8cef5342c1dc49fadb99d23fa7e66cf0ef
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  share_plus: c3fef564749587fc939ef86ffb283ceac0baf9f5
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f

PODFILE CHECKSUM: 9b685a7323e4608c146957cfdc22d54c5ae2c52f

COCOAPODS: 1.16.2
